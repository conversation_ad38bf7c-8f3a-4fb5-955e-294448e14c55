package com.bsm.v4.system.model.dto.business.transfer;

import com.caictframework.utils.annotation.TablePageNum;
import com.caictframework.utils.annotation.TablePageSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


import java.util.Date;
import java.util.List;

/**
 * Created by dengsy on 2020-05-08.
 * <p>
 * appDateStart：查询开始时间
 * appDateEnd：查询结束时间
 * userType: 运行商类型
 * <p>
 * jobName:任务名称
 * <p>
 * transportFileDTOList:所属exl文件集合
 */

@ApiModel(value = "approvalTransportJobDTO", description = "菜单对象")
public class ApprovalTransportJobDTO {

    private String id;
    private Date appDateStart;
    private Date appDateEnd;
    private String userType;
    private String type;
    private String fileId;

    private String jobName;
    //    private String appGuid;
    private int sectionNum;

    private String userName;
    /*
    * 区域名称
     */
    private String regionName;

    private Integer dataCount;

    @ApiModelProperty(value = "当前页码")
    @TablePageNum
    private int page;

    @ApiModelProperty(value = "每页显示数量")
    @TablePageSize
    private int rows;

    //附件信息
    private String fileGuid;
    private String token;
    private String fileName;
    private String fileState;

    private Date gmtCreate;
    private Date gmtModified;
    private Long isDeleted;
    /**
     * 基站GUID
     */
    private String jobId;
    /**
     * 审批状态
     */
    private String status;
    /**
     * 申请条件
     */
    private Long unitApproved;
    /**
     * 审核材料
     */
    private Long materialApproved;
    /**
     * user guid
     */
    private String opUser;
    /**
     * user guid
     */
    private String opUserGuid;
    /*
     *用户id
     */
    private String usersId;
    /**
     * 审核意见
     */
    private String opDetail;
    /**
     * 审核时间
     */
    private Date opTime;

    private String isCompare;

    //对比差异数量
    private Long diffCount;

    private String jobCode;

    //对比无差异数量
    private Long idenCount;

    //代数
    private String genNum;
    //数据类型
    private String dataType;
    //地区
    private String regionCode;
    //申请表编号
    private String appCode;

    //制式
    private String techType;

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public Long getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Long isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Long getUnitApproved() {
        return unitApproved;
    }

    public void setUnitApproved(Long unitApproved) {
        this.unitApproved = unitApproved;
    }

    public Long getMaterialApproved() {
        return materialApproved;
    }

    public void setMaterialApproved(Long materialApproved) {
        this.materialApproved = materialApproved;
    }

    public String getOpUser() {
        return opUser;
    }

    public void setOpUser(String opUser) {
        this.opUser = opUser;
    }

    public String getOpUserGuid() {
        return opUserGuid;
    }

    public void setOpUserGuid(String opUserGuid) {
        this.opUserGuid = opUserGuid;
    }

    public String getOpDetail() {
        return opDetail;
    }

    public void setOpDetail(String opDetail) {
        this.opDetail = opDetail;
    }

    public Date getOpTime() {
        return opTime;
    }

    public void setOpTime(Date opTime) {
        this.opTime = opTime;
    }

    public String getIsCompare() {
        return isCompare;
    }

    public void setIsCompare(String isCompare) {
        this.isCompare = isCompare;
    }

    public Long getDiffCount() {
        return diffCount;
    }

    public void setDiffCount(Long diffCount) {
        this.diffCount = diffCount;
    }

    public String getJobCode() {
        return jobCode;
    }

    public void setJobCode(String jobCode) {
        this.jobCode = jobCode;
    }

    public Long getIdenCount() {
        return idenCount;
    }

    public void setIdenCount(Long idenCount) {
        this.idenCount = idenCount;
    }

    public String getGenNum() {
        return genNum;
    }

    public void setGenNum(String genNum) {
        this.genNum = genNum;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public String getRegionCode() {
        return regionCode;
    }

    public void setRegionCode(String regionCode) {
        this.regionCode = regionCode;
    }

    public String getAppCode() {
        return appCode;
    }

    public void setAppCode(String appCode) {
        this.appCode = appCode;
    }

    public String getTechType() {
        return techType;
    }

    public void setTechType(String techType) {
        this.techType = techType;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getJobId() {
        return jobId;
    }

    public void setJobId(String jobId) {
        this.jobId = jobId;
    }

    //所属exl文件集合
    private List<TransportFileDTO> transportFileDTOList;

    public Date getAppDateStart() {
        return appDateStart;
    }

    public void setAppDateStart(Date appDateStart) {
        this.appDateStart = appDateStart;
    }

    public Date getAppDateEnd() {
        return appDateEnd;
    }

    public void setAppDateEnd(Date appDateEnd) {
        this.appDateEnd = appDateEnd;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getFileId() {
        return fileId;
    }

    public void setFileId(String fileId) {
        this.fileId = fileId;
    }

    public String getJobName() {
        return jobName;
    }

    public void setJobName(String jobName) {
        this.jobName = jobName;
    }

    public int getSectionNum() {
        return sectionNum;
    }

    public void setSectionNum(int sectionNum) {
        this.sectionNum = sectionNum;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getRows() {
        return rows;
    }

    public void setRows(int rows) {
        this.rows = rows;
    }

    public String getFileGuid() {
        return fileGuid;
    }

    public void setFileGuid(String fileGuid) {
        this.fileGuid = fileGuid;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFileState() {
        return fileState;
    }

    public void setFileState(String fileState) {
        this.fileState = fileState;
    }

    public List<TransportFileDTO> getTransportFileDTOList() {
        return transportFileDTOList;
    }

    public void setTransportFileDTOList(List<TransportFileDTO> transportFileDTOList) {
        this.transportFileDTOList = transportFileDTOList;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }


    public String getRegionName() {
        return regionName;
    }

    public void setRegionName(String regionName) {
        this.regionName = regionName;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUsersId() {
        return usersId;
    }

    public void setUsersId(String usersId) {
        this.usersId = usersId;
    }

    public Integer getDataCount() {
        return dataCount;
    }

    public void setDataCount(Integer dataCount) {
        this.dataCount = dataCount;
    }
}
