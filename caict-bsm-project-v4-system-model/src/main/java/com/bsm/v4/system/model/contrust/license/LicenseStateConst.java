package com.bsm.v4.system.model.contrust.license;

/**
 * <AUTHOR>
 * @version 1.0
 *  * 1：已发
 *  * 2：过期
 *  * 3：停用
 *  * 4：注销
 */
public class LicenseStateConst {
    public static final long LICENSE_REGULAR = 1;

    public static final long LICENSE_EXPIRE = 2;

    public static final long LICENSE_PAUSE = 3;

    public static final long LICENSE_DELETE = 4;

    public static final long LICENSE_WILL_EXPIRE = 5;
}
