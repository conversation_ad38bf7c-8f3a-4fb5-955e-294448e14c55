package com.bsm.v4.api.web.service.bussiness.station;

import com.bsm.v4.domain.security.service.business.license.LicenseService;
import com.bsm.v4.system.model.entity.business.license.RsbtLicense;
import com.caictframework.data.service.BasicWebService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by yanchengpeng on 2020/12/6.
 */
@Service
public class RsbtLicenseWebService extends BasicWebService {

    @Autowired
    private LicenseService licenseService;

    /**
     * 插入执照
     *
     * @param licenseList licenseList
     */
    public int insertBatch(List<RsbtLicense> licenseList) {
        if (licenseList.size() > 50) {
            int pageNum = licenseList.size() % 50 == 0 ? licenseList.size() / 50 : licenseList.size() / 50 + 1;
            for (int i = 1; i <= pageNum; i++) {
                List<RsbtLicense> licenses;
                if (i != pageNum) {
                    licenses = licenseList.subList((i - 1) * 50, i * 50);
                } else {
                    licenses = licenseList.subList((i - 1) * 50, licenseList.size());
                }
                licenseService.insertBatch(licenses);
            }
        } else {
            licenseService.insertBatch(licenseList);
        }
        return 0;
    }

    public RsbtLicense selectByStationGuid(String stationGuid) {
        return licenseService.selectByStationGuid(stationGuid);
    }

    public int updateBatch(List<RsbtLicense> licenses) {
        return licenseService.updateBatch(licenses);
    }

    public void delete(List<RsbtLicense> licenses) {
        List<String> ids = licenses.stream().map(RsbtLicense::getStationGuid).distinct().collect(Collectors.toList());
        licenseService.deleteByStationIds(ids);
    }

    public int updateBatchByStationGuid(List<String> stationGuids, String appCode) {
        return licenseService.updateBatchByStationGuid(stationGuids, appCode);
    }

    //批量根据stationGuid查找执照表
    public List<RsbtLicense> selectByStationGuids(List<String> stationGuids) {
        return licenseService.selectByStationGuids(stationGuids);
    }
}
