package com.bsm.v4.api.web.service.bussiness.transfer;

import com.bsm.v4.api.web.service.bussiness.station.*;
import com.bsm.v4.domain.security.service.security.OrgUsersService;
import com.bsm.v4.system.model.contrust.transfer.ApprovalTransportJobStateConst;
import com.bsm.v4.system.model.contrust.transfer.TransportJobStateConst;
import com.bsm.v4.system.model.dto.business.transfer.ProcessingDataDTO;
import com.bsm.v4.system.model.dto.security.OrgDTO;
import com.bsm.v4.system.model.entity.business.license.RsbtLicense;
import com.bsm.v4.system.model.entity.business.station.*;
import com.bsm.v4.system.model.entity.business.stationbak.RsbtStationBak;
import com.bsm.v4.system.model.entity.business.transfer.AsyncRawBts;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class DataApproveService {

    private static final Logger LOG = LoggerFactory.getLogger(DataApproveService.class);

    @Autowired
    private RsbtStationWebService rsbtStationWebService;
    @Autowired
    private RsbtStationAppendixWebService rsbtStationAppendixWebService;
    @Autowired
    private RsbtAntfeedWebService rsbtAntfeedWebService;
    @Autowired
    private RsbtAntfeedAppendixWebService rsbtAntfeedAppendixWebService;
    @Autowired
    private RsbtEquWebService rsbtEquWebService;
    @Autowired
    private RsbtEquAppendixWebService rsbtEquAppendixWebService;
    @Autowired
    private RsbtFreqWebService rsbtFreqWebService;
    @Autowired
    private RsbtFreqAppendixWebService rsbtFreqAppendixWebService;
    @Autowired
    private TransportScheduleWebService transportScheduleWebService;
    @Autowired
    private AsyncRawBtsWebService asyncRawBtsWebService;
    @Autowired
    private TransportJobWebService transportJobWebService;
    @Autowired
    private ApprovalTransportJobWebService approvalTransportJobWebService;
    @Autowired
    private ApprovalScheduleWebService approvalScheduleWebService;
    @Autowired
    private RsbtStationBakWebService rsbtStationBakWebService;
    @Autowired
    private RsbtStationTWebService rsbtStationTWebService;
    @Autowired
    private RsbtAntfeedTWebService rsbtAntfeedTWebService;
    @Autowired
    private RsbtEquTWebService rsbtEquTWebService;
    @Autowired
    private RsbtFreqTWebService rsbtFreqTWebService;
    @Autowired
    private RsbtNetWebService rsbtNetWebService;
    @Autowired
    private RsbtEafWebService rsbtEafWebService;
    @Autowired
    private OrgUsersService rsbtOrgService;
    @Autowired
    private RsbtLicenseWebService rsbtLicenseWebService;


    public void processingData(ProcessingDataDTO processingDataDTO) {

        /*
         * 1.新增
         * 2.修改
         * 3.注销
         * 4.延续
         */
        if ("1".equals(processingDataDTO.getDateType())) {
            processingDataInsert(processingDataDTO);
        } else if ("2".equals(processingDataDTO.getDateType())) {
            processingDataUpdate(processingDataDTO);
        } else if ("3".equals(processingDataDTO.getDateType())) {
            processingDataDelete(processingDataDTO);
        } else if ("4".equals(processingDataDTO.getDateType())) {
            processingDataContinue(processingDataDTO);
        }
    }

    //批量添加
    public void processingDataInsert(ProcessingDataDTO processingDataDTO) {

        List<RsbtNet> rsbtNetListInsert = processingDataDTO.getRsbtNetList();
        if (rsbtNetListInsert != null && rsbtNetListInsert.size() != 0)
            rsbtNetWebService.insertBatch(rsbtNetListInsert);

        List<RsbtEaf> rsbtEafList = processingDataDTO.getRsbtEafList();
        if (rsbtEafList != null && rsbtEafList.size() != 0) {
            rsbtEafWebService.insertBatch(rsbtEafList);
        }

        //批量添加基站
        List<RsbtStation> rsbtStationListInsert = processingDataDTO.getRsbtStationList();
        if (rsbtStationListInsert != null && rsbtStationListInsert.size() != 0) {
            rsbtStationWebService.insertBatch(rsbtStationListInsert);
        }
        //批量添加基站状态
        List<RsbtStationAppendix> rsbtStationAppendixListInsert = processingDataDTO.getRsbtStationAppendixList();
        if (rsbtStationAppendixListInsert != null && rsbtStationAppendixListInsert.size() != 0) {
            rsbtStationAppendixWebService.insertBatch(rsbtStationAppendixListInsert);
        }

        List<RsbtStationT> rsbtStationTList = processingDataDTO.getRsbtStationTList();
        if (rsbtStationTList != null && rsbtStationTList.size() != 0) {
            rsbtStationTWebService.insertBatch(rsbtStationTList);
        }

        List<RsbtStationBak> rsbtStationBakList = processingDataDTO.getRsbtStationBakList();
        if (rsbtStationBakList != null && rsbtStationBakList.size() != 0) {
            rsbtStationBakWebService.insertBatch(rsbtStationBakList);
        }

        //批量添加天线
        List<RsbtAntfeed> rsbtAntfeedListInsert = processingDataDTO.getRsbtAntfeedList();
        if (rsbtAntfeedListInsert != null && rsbtAntfeedListInsert.size() != 0) {
            rsbtAntfeedWebService.insertBatch(rsbtAntfeedListInsert);
        }
        //批量添加天线状态
        List<RsbtAntfeedAppendix> rsbtAntfeedAppendixListInsert = processingDataDTO.getRsbtAntfeedAppendixList();
        if (rsbtAntfeedAppendixListInsert != null && rsbtAntfeedAppendixListInsert.size() != 0) {
            rsbtAntfeedAppendixWebService.insertBatch(rsbtAntfeedAppendixListInsert);
        }
        // 天线冗余
        List<RsbtAntfeedT> rsbtAntfeedTList = processingDataDTO.getRsbtAntfeedTList();
        if (rsbtAntfeedTList != null && rsbtAntfeedTList.size() != 0) {
            rsbtAntfeedTWebService.insertBatch(rsbtAntfeedTList);
        }
        //批量添加设备
        List<RsbtEqu> rsbtEquListInsert = processingDataDTO.getRsbtEquList();
        if (rsbtEquListInsert != null && rsbtEquListInsert.size() != 0) {
            rsbtEquWebService.insertBatch(rsbtEquListInsert);
        }
        //批量添加设备状态
        List<RsbtEquAppendix> rsbtEquAppendixListInsert = processingDataDTO.getRsbtEquAppendixList();
        if (rsbtEquAppendixListInsert != null && rsbtEquAppendixListInsert.size() != 0) {
            rsbtEquAppendixWebService.insertBatch(rsbtEquAppendixListInsert);
        }
        // 设备冗余
        List<RsbtEquT> rsbtEquTList = processingDataDTO.getRsbtEquTList();
        if (rsbtEquTList != null && rsbtEquTList.size() != 0) {
            rsbtEquTWebService.insertBatch(rsbtEquTList);
        }
        //批量添加参数
        List<RsbtFreq> rsbtFreqListInsert = processingDataDTO.getRsbtFreqList();
        if (rsbtFreqListInsert != null && rsbtFreqListInsert.size() != 0) {
            rsbtFreqWebService.insertBatch(rsbtFreqListInsert);
        }
        //批量添加参数状态
        List<RsbtFreqAppendix> rsbtFreqAppendixListInsert = processingDataDTO.getRsbtFreqAppendixList();
        if (rsbtFreqAppendixListInsert != null && rsbtFreqAppendixListInsert.size() != 0) {
            rsbtFreqAppendixWebService.insertBatch(rsbtFreqAppendixListInsert);
        }
        // 参数冗余
        List<RsbtFreqT> rsbtFreqTList = processingDataDTO.getRsbtFreqTList();
        if (rsbtFreqTList != null && rsbtFreqTList.size() != 0) {
            rsbtFreqTWebService.insertBatch(rsbtFreqTList);
        }

        //批量添加总表
        List<AsyncRawBts> asyncRawBtsInsert = processingDataDTO.getAsyncRawBtsList();
        if (asyncRawBtsInsert != null && asyncRawBtsInsert.size() != 0) {
            asyncRawBtsWebService.insertBatch(asyncRawBtsInsert);
        }
    }

    //批量修改
    public void processingDataUpdate(ProcessingDataDTO processingDataDTO) {
        //批量修改基站
        List<RsbtStation> rsbtStationListUpdate = processingDataDTO.getRsbtStationList();
        if (rsbtStationListUpdate != null && rsbtStationListUpdate.size() != 0) {
            rsbtStationWebService.updateBatch(rsbtStationListUpdate);
        }

        List<RsbtStationBak> rsbtStationBakList = processingDataDTO.getRsbtStationBakList();
        if (rsbtStationBakList != null && rsbtStationBakList.size() != 0) {
            rsbtStationBakWebService.insertBatch(rsbtStationBakList);
        }

        List<RsbtStationT> rsbtStationTList = processingDataDTO.getRsbtStationTList();
        if (rsbtStationTList != null && rsbtStationTList.size() != 0) {
            rsbtStationTWebService.updateBatch(rsbtStationTList);
        }

        List<RsbtStationAppendix> rsbtStationAppendixListUpdate = processingDataDTO.getRsbtStationAppendixList();
        if (rsbtStationAppendixListUpdate != null && rsbtStationAppendixListUpdate.size() != 0) {
            rsbtStationAppendixWebService.updateBatch(rsbtStationAppendixListUpdate);
        }

        //批量修改总表
        List<AsyncRawBts> asyncRawBtsAdd = new ArrayList<>();
        List<AsyncRawBts> asyncRawBtsDel = new ArrayList<>();
        List<AsyncRawBts> asyncRawBtsModify = new ArrayList<>();
        List<AsyncRawBts> asyncRawBtsUpdate = processingDataDTO.getAsyncRawBtsList();
        if (asyncRawBtsUpdate != null && asyncRawBtsUpdate.size() != 0) {
            for (AsyncRawBts asyncRawBts : asyncRawBtsUpdate) {
                if ("1".equals(asyncRawBts.getDataType())) {
                    // 变更里边的新增扇区
                    asyncRawBtsAdd.add(asyncRawBts);
                    // 变更里边的删除扇区
                } else if ("3".equals(asyncRawBts.getDataType())) {
                    asyncRawBtsDel.add(asyncRawBts);
                } else {
                    // 变更里边的修改扇区
                    asyncRawBtsModify.add(asyncRawBts);
                }
            }
            if (asyncRawBtsAdd.size() > 0) {
                // 新增插入
                asyncRawBtsWebService.insertBatch(asyncRawBtsAdd);
            }
            if (asyncRawBtsDel.size() > 0) {
                // 删除修改状态
//                asyncRawBtsWebService.updateBatch(asyncRawBtsDel);
            }
            if (asyncRawBtsModify.size() > 0) {
                asyncRawBtsWebService.updateBatch(asyncRawBtsModify);
            }
        }

        //批量修改天线冗余
        List<RsbtAntfeedT> rsbtAntfeedtListAdd = new ArrayList<>();
        List<RsbtAntfeedT> rsbtAntfeedtListModify = new ArrayList<>();
        List<RsbtAntfeedT> rsbtAntfeedtListDel = new ArrayList<>();
        List<RsbtAntfeedT> rsbtAntfeedtListUpdate = processingDataDTO.getRsbtAntfeedTList();
        if (rsbtAntfeedtListUpdate != null && rsbtAntfeedtListUpdate.size() != 0) {
            for (RsbtAntfeedT rsbtAntfeedt : rsbtAntfeedtListUpdate) {
                if (asyncRawBtsAdd.size() > 0) {
                    for (AsyncRawBts asyncRawBts : asyncRawBtsAdd) {
                        if (asyncRawBts.getCellId().equals(rsbtAntfeedt.getAtCcode())) {
                            rsbtAntfeedtListAdd.add(rsbtAntfeedt);
                            break;
                        }
                    }
                }
                if (asyncRawBtsDel.size() > 0) {
                    for (AsyncRawBts asyncRawBts : asyncRawBtsDel) {
                        if (asyncRawBts.getCellId().equals(rsbtAntfeedt.getAtCcode())) {
                            rsbtAntfeedtListDel.add(rsbtAntfeedt);
                            break;
                        }
                    }
                }
                if (asyncRawBtsModify.size() > 0) {
                    for (AsyncRawBts asyncRawBts : asyncRawBtsModify) {
                        if (asyncRawBts.getCellId().equals(rsbtAntfeedt.getAtCcode())) {
                            rsbtAntfeedtListModify.add(rsbtAntfeedt);
                            break;
                        }
                    }
                }
            }
            if (rsbtAntfeedtListAdd.size() > 0) {
                rsbtAntfeedTWebService.insertBatch(rsbtAntfeedtListAdd);
            }
            if (rsbtAntfeedtListModify.size() > 0) {
                rsbtAntfeedTWebService.updateBatch(rsbtAntfeedtListModify);
            }
            if (rsbtAntfeedtListDel.size() > 0) {
                rsbtAntfeedTWebService.deleteBatch(rsbtAntfeedtListDel);
            }
        }

        //批量修改天线
        List<RsbtAntfeed> rsbtAntfeedListAdd = new ArrayList<>();
        List<RsbtAntfeed> rsbtAntfeedListModify = new ArrayList<>();
        List<RsbtAntfeed> rsbtAntfeedListDel = new ArrayList<>();
        List<RsbtAntfeed> rsbtAntfeedListUpdate = processingDataDTO.getRsbtAntfeedList();
        if (rsbtAntfeedListUpdate != null && rsbtAntfeedListUpdate.size() != 0) {
            for (RsbtAntfeed rsbtAntfeed : rsbtAntfeedListUpdate) {
                if (rsbtAntfeedtListAdd.size() > 0) {
                    for (RsbtAntfeedT rsbtAntfeedT : rsbtAntfeedtListAdd) {
                        if (rsbtAntfeedT.getGuid().equals(rsbtAntfeed.getGuid())) {
                            rsbtAntfeedListAdd.add(rsbtAntfeed);
                            break;
                        }
                    }
                }
                if (rsbtAntfeedtListDel.size() > 0) {
                    for (RsbtAntfeedT rsbtAntfeedT : rsbtAntfeedtListDel) {
                        if (rsbtAntfeedT.getGuid().equals(rsbtAntfeed.getGuid())) {
                            rsbtAntfeedListDel.add(rsbtAntfeed);
                            break;
                        }
                    }
                }
                if (rsbtAntfeedtListModify.size() > 0) {
                    for (RsbtAntfeedT rsbtAntfeedT : rsbtAntfeedtListModify) {
                        if (rsbtAntfeedT.getGuid().equals(rsbtAntfeed.getGuid())) {
                            rsbtAntfeedListModify.add(rsbtAntfeed);
                            break;
                        }
                    }
                }
            }
            if (rsbtAntfeedListAdd.size() > 0) {
                rsbtAntfeedWebService.insertBatch(rsbtAntfeedListAdd);
            }
            if (rsbtAntfeedListModify.size() > 0) {
                rsbtAntfeedWebService.updateBatch(rsbtAntfeedListModify);
            }
            if (rsbtAntfeedListDel.size() > 0) {
                rsbtAntfeedWebService.deleteBatch(rsbtAntfeedListDel);
            }
        }

        //批量添加天线状态
        List<RsbtAntfeedAppendix> antfeedAppendixListAdd = new ArrayList<>();
        List<RsbtAntfeedAppendix> antfeedAppendixListDel = new ArrayList<>();
        List<RsbtAntfeedAppendix> rsbtAntfeedAppendixListInsert = processingDataDTO.getRsbtAntfeedAppendixList();
        if (rsbtAntfeedAppendixListInsert != null && rsbtAntfeedAppendixListInsert.size() != 0) {
            for (RsbtAntfeedAppendix rsbtAntfeedAppendix : rsbtAntfeedAppendixListInsert) {
                // 比较新增的扇区，以插入扇区
                if (rsbtAntfeedListAdd.size() > 0) {
                    for (RsbtAntfeed rsbtAntfeed : rsbtAntfeedListAdd) {
                        if (rsbtAntfeedAppendix.getGuid().equals(rsbtAntfeed.getGuid())) {
                            antfeedAppendixListAdd.add(rsbtAntfeedAppendix);
                            break;
                        }
                    }
                }
                // 比较删除的扇区，以删除扇区
                if (rsbtAntfeedListDel.size() > 0) {
                    for (RsbtAntfeed rsbtAntfeed : rsbtAntfeedListDel) {
                        if (rsbtAntfeedAppendix.getGuid().equals(rsbtAntfeed.getGuid())) {
                            antfeedAppendixListDel.add(rsbtAntfeedAppendix);
                            break;
                        }
                    }
                }
            }
            // 插入扇区
            if (antfeedAppendixListAdd.size() > 0) {
                rsbtAntfeedAppendixWebService.insertBatch(antfeedAppendixListAdd);
            }
            // 删除扇区
            if (antfeedAppendixListDel.size() > 0) {
                rsbtAntfeedAppendixWebService.deleteBatchByAntGuid(rsbtAntfeedListDel);
            }
        }

        // 设备冗余
        List<RsbtEquT> rsbtEqutListAdd = new ArrayList<>();
        List<RsbtEquT> rsbtEqutListModify = new ArrayList<>();
        List<RsbtEquT> rsbtEqutListDel = new ArrayList<>();
        List<RsbtEquT> rsbtEqutListUpdate = processingDataDTO.getRsbtEquTList();
        if (rsbtEqutListUpdate != null && rsbtEqutListUpdate.size() != 0) {
            for (RsbtEquT rsbtEquT : rsbtEqutListUpdate) {
                if (asyncRawBtsAdd.size() > 0) {
                    for (AsyncRawBts asyncRawBts : asyncRawBtsAdd) {
                        if (asyncRawBts.getCellId().equals(rsbtEquT.getEtEquCcode())) {
                            rsbtEqutListAdd.add(rsbtEquT);
                            break;
                        }
                    }
                }
                if (asyncRawBtsDel.size() > 0) {
                    for (AsyncRawBts asyncRawBts : asyncRawBtsDel) {
                        if (asyncRawBts.getCellId().equals(rsbtEquT.getEtEquCcode())) {
                            rsbtEqutListDel.add(rsbtEquT);
                            break;
                        }
                    }
                }
                if (asyncRawBtsModify.size() > 0) {
                    for (AsyncRawBts asyncRawBts : asyncRawBtsModify) {
                        if (asyncRawBts.getCellId().equals(rsbtEquT.getEtEquCcode())) {
                            rsbtEqutListModify.add(rsbtEquT);
                            break;
                        }
                    }
                }
            }
            if (rsbtEqutListAdd.size() > 0) {
                rsbtEquTWebService.insertBatch(rsbtEqutListAdd);
            }
            if (rsbtEqutListModify.size() > 0) {
                rsbtEquTWebService.updateBatch(rsbtEqutListModify);
            }
            if (rsbtEqutListDel.size() > 0) {
                rsbtEquTWebService.deleteBatch(rsbtEqutListDel);
            }
        }
        //批量修改设备
        List<RsbtEqu> rsbtEquListAdd = new ArrayList<>();
        List<RsbtEqu> rsbtEquListModify = new ArrayList<>();
        List<RsbtEqu> rsbtEquListDel = new ArrayList<>();
        List<RsbtEqu> rsbtEquListUpdate = processingDataDTO.getRsbtEquList();
        if (rsbtEquListUpdate != null && rsbtEquListUpdate.size() != 0) {
            for (RsbtEqu rsbtEqu : rsbtEquListUpdate) {
                if (rsbtEqutListAdd.size() > 0) {
                    for (RsbtEquT rsbtEquT : rsbtEqutListAdd) {
                        if (rsbtEquT.getGuid().equals(rsbtEqu.getGuid())) {
                            rsbtEquListAdd.add(rsbtEqu);
                            break;
                        }
                    }
                }
                if (rsbtEqutListDel.size() > 0) {
                    for (RsbtEquT rsbtEquT : rsbtEqutListDel) {
                        if (rsbtEquT.getGuid().equals(rsbtEqu.getGuid())) {
                            rsbtEquListDel.add(rsbtEqu);
                            break;
                        }
                    }
                }
                if (rsbtEqutListModify.size() > 0) {
                    for (RsbtEquT rsbtEquT : rsbtEqutListModify) {
                        if (rsbtEquT.getGuid().equals(rsbtEqu.getGuid())) {
                            rsbtEquListModify.add(rsbtEqu);
                            break;
                        }
                    }
                }
            }
            if (rsbtEquListAdd.size() > 0) {
                rsbtEquWebService.insertBatch(rsbtEquListAdd);
            }
            if (rsbtEquListModify.size() > 0) {
                rsbtEquWebService.updateBatch(rsbtEquListModify);
            }
            if (rsbtEquListDel.size() > 0) {
                rsbtEquWebService.deleteBatch(rsbtEquListDel);
            }
        }

        //批量添加设备状态
        List<RsbtEquAppendix> rsbtEquAppendixListAdd = new ArrayList<>();
        List<RsbtEquAppendix> rsbtEquAppendixListDel = new ArrayList<>();
        List<RsbtEquAppendix> rsbtEquAppendixListModify = new ArrayList<>();
        List<RsbtEquAppendix> rsbtEquAppendixListInsert = processingDataDTO.getRsbtEquAppendixList();
        if (rsbtEquAppendixListInsert != null && rsbtEquAppendixListInsert.size() != 0) {
            for (RsbtEquAppendix rsbtEquAppendix : rsbtEquAppendixListInsert) {
                // 比较新增的扇区，以插入扇区
                if (rsbtEqutListAdd.size() > 0) {
                    for (RsbtEquT rsbtEqut : rsbtEqutListAdd) {
                        if (rsbtEqut.getGuid().equals(rsbtEquAppendix.getGuid())) {
                            rsbtEquAppendixListAdd.add(rsbtEquAppendix);
                            break;
                        }
                    }
                }
                // 比较删除的扇区，以删除扇区
                if (rsbtEqutListDel.size() > 0) {
                    for (RsbtEquT rsbtEqut : rsbtEqutListDel) {
                        if (rsbtEqut.getGuid().equals(rsbtEquAppendix.getGuid())) {
                            rsbtEquAppendixListDel.add(rsbtEquAppendix);
                            break;
                        }
                    }
                }
                // 比较修改的扇区，以修改扇区
                if (rsbtEqutListModify.size() > 0) {
                    for (RsbtEquT rsbtEqut : rsbtEqutListModify) {
                        if (rsbtEqut.getGuid().equals(rsbtEquAppendix.getGuid())) {
                            rsbtEquAppendixListModify.add(rsbtEquAppendix);
                            break;
                        }
                    }
                }
            }
            // 插入扇区
            if (rsbtEquAppendixListAdd.size() > 0) {
                rsbtEquAppendixWebService.insertBatch(rsbtEquAppendixListAdd);
            }
            // 删除扇区
            if (rsbtEquAppendixListDel.size() > 0) {
                rsbtEquAppendixWebService.deleteBatchByEquGuid(rsbtEquListDel);
            }
            // 修改扇区
            if (rsbtEquAppendixListModify.size() > 0) {
                rsbtEquAppendixWebService.updateBatch(rsbtEquAppendixListModify);
            }
        }


        //批量修改参数
        List<RsbtFreqT> rsbtFreqtListAdd = new ArrayList<>();
        List<RsbtFreqT> rsbtFreqtListModify = new ArrayList<>();
        List<RsbtFreqT> rsbtFreqtListDel = new ArrayList<>();
        List<RsbtFreqT> rsbtFreqtListUpdate = processingDataDTO.getRsbtFreqTList();
        if (rsbtFreqtListUpdate != null && rsbtFreqtListUpdate.size() != 0) {
            for (RsbtFreqT rsbtFreqt : rsbtFreqtListUpdate) {
                if (asyncRawBtsAdd.size() > 0) {
                    for (AsyncRawBts asyncRawBts : asyncRawBtsAdd) {
                        if (asyncRawBts.getCellId().equals(rsbtFreqt.getFtFreqCcode())) {
                            rsbtFreqtListAdd.add(rsbtFreqt);
                            break;
                        }
                    }
                }
                if (asyncRawBtsDel.size() > 0) {
                    for (AsyncRawBts asyncRawBts : asyncRawBtsDel) {
                        if (asyncRawBts.getCellId().equals(rsbtFreqt.getFtFreqCcode())) {
                            rsbtFreqtListDel.add(rsbtFreqt);
                            break;
                        }
                    }
                }
                if (asyncRawBtsModify.size() > 0) {
                    for (AsyncRawBts asyncRawBts : asyncRawBtsModify) {
                        if (asyncRawBts.getCellId().equals(rsbtFreqt.getFtFreqCcode())) {
                            rsbtFreqtListModify.add(rsbtFreqt);
                            break;
                        }
                    }
                }
            }
            if (rsbtFreqtListAdd.size() > 0) {
                rsbtFreqTWebService.insertBatch(rsbtFreqtListAdd);
            }
            if (rsbtFreqtListModify.size() > 0) {
                rsbtFreqTWebService.updateBatch(rsbtFreqtListModify);
            }
            if (rsbtFreqtListDel.size() > 0) {
                rsbtFreqTWebService.deleteBatch(rsbtFreqtListDel);
            }
        }

        //批量修改参数
        List<RsbtFreq> rsbtFreqListAdd = new ArrayList<>();
        List<RsbtFreq> rsbtFreqListModify = new ArrayList<>();
        List<RsbtFreq> rsbtFreqListDel = new ArrayList<>();
        List<RsbtFreq> rsbtFreqListUpdate = processingDataDTO.getRsbtFreqList();
        if (rsbtFreqListUpdate != null && rsbtFreqListUpdate.size() != 0) {
            for (RsbtFreq rsbtFreq : rsbtFreqListUpdate) {
                if (rsbtFreqtListAdd.size() > 0) {
                    for (RsbtFreqT rsbtFreqt : rsbtFreqtListAdd) {
                        if (rsbtFreqt.getGuid().equals(rsbtFreq.getGuid())) {
                            rsbtFreqListAdd.add(rsbtFreq);
                            break;
                        }
                    }
                }
                if (rsbtFreqtListDel.size() > 0) {
                    for (RsbtFreqT rsbtFreqt : rsbtFreqtListDel) {
                        if (rsbtFreqt.getGuid().equals(rsbtFreq.getGuid())) {
                            rsbtFreqListDel.add(rsbtFreq);
                            break;
                        }
                    }
                }
                if (rsbtFreqtListModify.size() > 0) {
                    for (RsbtFreqT rsbtFreqt : rsbtFreqtListModify) {
                        if (rsbtFreqt.getGuid().equals(rsbtFreq.getGuid())) {
                            rsbtFreqListModify.add(rsbtFreq);
                            break;
                        }
                    }
                }
            }
            if (rsbtFreqListAdd.size() > 0) {
                rsbtFreqWebService.insertBatch(rsbtFreqListAdd);
            }
            if (rsbtFreqListModify.size() > 0) {
                rsbtFreqWebService.updateBatch(rsbtFreqListModify);
            }
            if (rsbtFreqListDel.size() > 0) {
                rsbtFreqWebService.deleteBatch(rsbtFreqListDel);
            }
        }

        //批量添加参数状态
        List<RsbtFreqAppendix> rsbtFreqAppendixListAdd = new ArrayList<>();
        List<RsbtFreqAppendix> rsbtFreqAppendixListDel = new ArrayList<>();
        List<RsbtFreqAppendix> rsbtFreqAppendixListModify = new ArrayList<>();
        List<RsbtFreqAppendix> rsbtFreqAppendixListInsert = processingDataDTO.getRsbtFreqAppendixList();
        if (rsbtFreqAppendixListInsert != null && rsbtFreqAppendixListInsert.size() != 0) {
            for (RsbtFreqAppendix rsbtFreqAppendix : rsbtFreqAppendixListInsert) {
                if (rsbtFreqtListAdd.size() > 0) {
                    for (RsbtFreqT rsbtFreqt : rsbtFreqtListAdd) {
                        if (rsbtFreqt.getGuid().equals(rsbtFreqAppendix.getGuid())) {
                            rsbtFreqAppendixListAdd.add(rsbtFreqAppendix);
                            break;
                        }
                    }
                }
                if (rsbtFreqtListDel.size() > 0) {
                    for (RsbtFreqT rsbtFreqt : rsbtFreqtListDel) {
                        if (rsbtFreqt.getGuid().equals(rsbtFreqAppendix.getGuid())) {
                            rsbtFreqAppendixListDel.add(rsbtFreqAppendix);
                            break;
                        }
                    }
                }
                if (rsbtFreqtListModify.size() > 0) {
                    for (RsbtFreqT rsbtFreqt : rsbtFreqtListModify) {
                        if (rsbtFreqt.getGuid().equals(rsbtFreqAppendix.getGuid())) {
                            rsbtFreqAppendixListModify.add(rsbtFreqAppendix);
                            break;
                        }
                    }
                }
            }
            // 插入扇区
            if (rsbtFreqAppendixListAdd.size() > 0) {
                rsbtFreqAppendixWebService.insertBatch(rsbtFreqAppendixListAdd);
            }
            // 删除扇区
            if (rsbtFreqAppendixListDel.size() > 0) {
                rsbtFreqAppendixWebService.deleteBatchByFreqGuid(rsbtFreqListDel);
            }
            // 修改扇区
            if (rsbtFreqAppendixListModify.size() > 0) {
                rsbtFreqAppendixWebService.updateBatch(rsbtFreqAppendixListModify);
            }
        }
    }

    //批量删除
    public void processingDataDelete(ProcessingDataDTO processingDataDTO) {

        //批量删除天线
        List<RsbtAntfeed> rsbtAntfeedListDelete = processingDataDTO.getRsbtAntfeedList();
        if (rsbtAntfeedListDelete != null && rsbtAntfeedListDelete.size() != 0) {
            rsbtAntfeedAppendixWebService.deleteBatchByAntGuid(rsbtAntfeedListDelete);
        }

        //批量删除设备
        List<RsbtEqu> rsbtEquListDelete = processingDataDTO.getRsbtEquList();
        if (rsbtEquListDelete != null && rsbtEquListDelete.size() != 0) {
            rsbtEquAppendixWebService.deleteBatchByEquGuid(rsbtEquListDelete);
        }

        //批量删除参数
        List<RsbtFreq> rsbtFreqListDelete = processingDataDTO.getRsbtFreqList();
        if (rsbtFreqListDelete != null && rsbtFreqListDelete.size() != 0) {
            rsbtFreqAppendixWebService.deleteBatchByFreqGuid(rsbtFreqListDelete);
        }

        //批量删除总表
        List<AsyncRawBts> asyncRawBtsDelete = processingDataDTO.getAsyncRawBtsList();
        if (asyncRawBtsDelete != null && asyncRawBtsDelete.size() != 0) {
            asyncRawBtsWebService.deleteBatch(asyncRawBtsDelete);
        }

        //批量插入基站bak
        List<RsbtStationBak> stationBakList = processingDataDTO.getRsbtStationBakList();
        if (stationBakList != null && stationBakList.size() != 0) {
            rsbtStationBakWebService.insertBatch(stationBakList);
        }

//        //批量删除基站
//        List<RsbtStation> rsbtStationList = processingDataDTO.getRsbtStationList();
//        if(rsbtStationList != null && rsbtStationList.size()!= 0){
//            rsbtStationWebService.delete(rsbtStationList);
//        }

//        //删除执照表
//        rsbtLicenseWebService.delete(rsbtStationList);
//        List<RsbtStation> rsbtStationList = processingDataDTO.getlicen();
//        if(rsbtStationList != null && rsbtStationList.size()!= 0){
//            rsbtLicenseWebService.delete(rsbtStationList);
//        }

        //获取需要注销的基站数据
        List<RsbtStationAppendix> rsbtStationListDeleted = rsbtStationAppendixWebService.findAllByNotSection();

        if (rsbtStationListDeleted != null && rsbtStationListDeleted.size() != 0) {
            //删除基站表
            rsbtStationAppendixWebService.deleteBatchByStationGuid(rsbtStationListDeleted);

            //删除执照表
//            rsbtLicenseWebService.delete(processingDataDTO.getLicenseList());
        }
    }


    /**
     * 执照延续
     */
    private void processingDataContinue(ProcessingDataDTO processingDataDTO) {
         List<RsbtLicense> rsbtLicenseList = processingDataDTO.getRsbtLicenseList();
         if (rsbtLicenseList != null && rsbtLicenseList.size() != 0) {
             //根据StationGuid排序并查询获取执照表
             List<String> ids = rsbtLicenseList.stream().map(RsbtLicense::getStationGuid).distinct().collect(Collectors.toList());
             List<RsbtLicense> licenseList = rsbtLicenseWebService.selectByStationGuids(ids);
             //根据截止日期 年份+3 并更新执照表截止日
             for (RsbtLicense rsbtLicense : licenseList){
                 if(rsbtLicense.getLicenseDateE() != null){
                     //更新有效期
                     rsbtLicense.setLicenseDateE(extendLicenseExpiration(rsbtLicense.getLicenseDateE()));
                 }
             }
             rsbtLicenseWebService.updateBatch(licenseList);
         }
    }

    /**
     * 更新License有效期（当前年份+3年）
     */
    public Date extendLicenseExpiration(Date licenseDateE) {
            Instant instant = licenseDateE.toInstant();
            ZonedDateTime zonedDateTime = instant.atZone(ZoneId.systemDefault());
            LocalDate newDate = zonedDateTime.toLocalDate().plusYears(3);
            return Date.from(newDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
    }

    public boolean checkData(String userId, Long jobId) {
        //判断是否还有待办任务
        if (transportScheduleWebService.selectCountByJobGuid(jobId) == 0 && approvalTransportJobWebService.findNotComplete(jobId, ApprovalTransportJobStateConst.COMPLETE) == 0) {
            //删除正式库中注销的数据
            //查询营运商对应的公司
            OrgDTO rsbtOrg = rsbtOrgService.findRsbtOrgByUserId(userId);
            asyncRawBtsWebService.deleteAllByUserDataType(rsbtOrg.getUserType(), "3");
            //删除审核待办数据
            approvalScheduleWebService.deleteAllByJob(jobId);
            //修改任务状态为完结
            transportJobWebService.updateIsCompareByGuid(TransportJobStateConst.COMPLETE, jobId);
            return true;
        } else {
            return false;
        }
    }

}
