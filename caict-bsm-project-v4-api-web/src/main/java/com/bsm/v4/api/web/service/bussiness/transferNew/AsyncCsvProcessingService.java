package com.bsm.v4.api.web.service.bussiness.transferNew;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bsm.v4.api.web.service.bussiness.transfer.ApprovalTransportJobWebService;
import com.bsm.v4.api.web.service.bussiness.transfer.TransportJobWebService;
import com.bsm.v4.domain.security.service.business.rule.FsaCheckRuleService;
import com.bsm.v4.domain.security.service.business.transfer.TransportFileService;
import com.bsm.v4.domain.security.service.business.transfer.TransportScheduleService;
import com.bsm.v4.domain.security.service.business.transferNew.*;
import com.bsm.v4.system.model.dto.business.rule.FsaCheckRuleDTO;
import com.bsm.v4.system.model.dto.security.RegionDTO;
import com.bsm.v4.system.model.dto.security.UsersDTO;
import com.bsm.v4.system.model.entity.business.transfer.TransportFile;
import com.bsm.v4.system.model.entity.business.transfer.TransportSchedule;
import com.bsm.v4.system.model.entity.business.transferNew.TransportJobBranchNew;
import com.bsm.v4.system.model.entity.business.transferNew.TransportJobNew;
import com.bsm.v4.system.model.entity.business.transferNew.TransportRawBtsDealLogNew;
import com.bsm.v4.system.model.entity.business.transferNew.TransportRawBtsDealNew;
import com.bsm.v4.system.model.vo.business.transferNew.CsvValidationResultVO;
import com.bsm.v4.system.model.vo.business.transferNew.JobMetaVO;
import com.caictframework.data.service.BasicWebService;
import com.caictframework.utils.service.RedisService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 异步CSV处理服务
 * 负责处理大型CSV文件的异步解析和数据处理
 *
 * <AUTHOR>
 * @date 2025-07-15
 */
@Service
public class AsyncCsvProcessingService extends BasicWebService {

    private static final Logger logger = LoggerFactory.getLogger(AsyncCsvProcessingService.class);

    @Resource
    private ThreadPoolTaskExecutor asyncExecutor;

    @Autowired
    private TransportJobNewService transportJobNewService;

    @Autowired
    private TransportFileService transportFileService;

    @Autowired
    private TransportJobNewWebService transportJobNewWebService;

    @Autowired
    private TransportRawBtsDealNewService transportRawBtsDealNewService;

    @Autowired
    private TransportRawBtsDealLogNewService transportRawBtsDealLogNewService;

    @Autowired
    private TransportJobBranchNewService transportJobBranchNewService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private FsaCheckRuleService fsaCheckRuleService;

    @Autowired
    private ApprovalTransportJobWebService approvalTransportJobWebService;

    @Autowired
    private TransportJobWebService transportJobWebService;

    @Autowired
    private TransportScheduleService transportScheduleService;

    /**
     * 异步处理CSV文件
     * 该方法会在单独的线程中执行，不会阻塞调用方
     * @param jobId 任务ID
     * @param files 文件列表
     * @param usersDTO 用户信息
     */
    @Async("asyncExecutor")
    public CompletableFuture<Void> processFilesAsync(Long jobId, List<TransportFile> files, UsersDTO usersDTO) {
        logger.info("=== 异步方法已进入 === 线程: {}", Thread.currentThread().getName());
        logger.info("开始异步处理任务 {} 的CSV文件，文件数量: {}", jobId, files.size());

        try {
            // 处理文件（状态已在submitJob中更新为processing）
            Map<String, Object> processingResult = processFiles(jobId, files, usersDTO);

            // 更新任务状态为完成
            boolean hasError = (boolean) processingResult.getOrDefault("hasProcessingError", false);
            String finalStatus = hasError ? "completed_with_errors" : "completed";
            String message = hasError ? "CSV处理完成，但存在错误" : "CSV处理完成";
            updateJobStatus(jobId, finalStatus, message);

            // 保存处理结果
            saveProcessingResult(jobId, processingResult);

            logger.info("任务 {} 异步处理完成，最终状态: {}", jobId, finalStatus);
            return CompletableFuture.completedFuture(null);

        } catch (Exception e) {
            logger.error("任务 {} 异步处理失败", jobId, e);
            // 更新任务状态为失败
            updateJobStatus(jobId, "failed", "处理失败: " + e.getMessage());
            throw new RuntimeException("异步处理CSV文件失败", e);
        }
    }

    /**
     * 处理文件（与原processSubmissionData方法逻辑相同，但移除事务注解）
     */
    private Map<String, Object> processFiles(Long jobId, List<TransportFile> files, UsersDTO usersDTO) {
        Map<String, Object> result = new HashMap<>();
        boolean hasProcessingError = false;
        int totalProcessedFiles = 0;
        int successfulFiles = 0;
        int failedFiles = 0;

        logger.info("processFiles: 开始处理任务 {} 的文件，文件数量: {}", jobId, files.size());

        // 获取任务信息，用于传递applyType给校验方法
        TransportJobNew jobInfo = transportJobNewService.findById(jobId);
        if (jobInfo == null) {
            result.put("totalProcessedFiles", 0);
            result.put("successfulFiles", 0);
            result.put("failedFiles", 0);
            result.put("hasProcessingError", true);
            return result;
        }

        // 只处理CSV(fileType="1")且未校验(status=1)的文件
        for (TransportFile file : files) {
            logger.info("检查文件: ID={}, fileType={}, status={}", file.getId(), file.getFileType(), file.getStatus());
            if (!("1".equals(file.getFileType()) && file.getStatus() != null && file.getStatus() == 1)) {
                logger.info("跳过文件: ID={}, 原因: fileType={} != '1' 或 status={} != 1",
                    file.getId(), file.getFileType(), file.getStatus());
                continue; // 跳过非CSV或已校验的文件
            }
            logger.info("开始处理文件: ID={}, filePath={}", file.getId(), file.getFilePath());

            totalProcessedFiles++;

            try {
                JobMetaVO jobMeta = new JobMetaVO();
                jobMeta.setJobId(jobId);
                jobMeta.setApplyType(jobInfo.getApplyType()); // 设置申请类型，用于核心表存在性校验

                logger.info("准备解析CSV文件: {}", file.getFilePath());
                // 解析和校验CSV文件，传递文件ID以支持HDFS
                Map<String, Object> checkResult = transportJobNewWebService.parseAndValidateCsv(
                    file.getFilePath(), jobMeta, "GBK", file.getId());
                logger.info("CSV解析完成，结果: {}", checkResult);
                CsvValidationResultVO validationResult = (CsvValidationResultVO) checkResult.get("data");

                if (Boolean.TRUE.equals(checkResult.get("success"))) {
                    // 文件解析成功，处理数据（包含与历史错误数据的比对）
                    processSubmissionDataWithHistoryComparison(
                            validationResult.getValidRows(),
                            validationResult.getInvalidRows(),
                            jobId, usersDTO
                    );

                    // 更新文件状态为已处理
                    updateFileStatus(file.getId(), 2);
                    successfulFiles++;
                } else {
                    // 文件解析失败
                    updateFileStatus(file.getId(), 3);
                    failedFiles++;
                    hasProcessingError = true;
                }
            } catch (Exception e) {
                // 处理异常
                logger.error("CSV处理异常: {} - {}", e.getClass().getSimpleName(), e.getMessage(), e);
                updateFileStatus(file.getId(), 3);
                failedFiles++;
                hasProcessingError = true;
            }
        }

        result.put("totalProcessedFiles", totalProcessedFiles);
        result.put("successfulFiles", successfulFiles);
        result.put("failedFiles", failedFiles);
        result.put("hasProcessingError", hasProcessingError);

        logger.info("processFiles完成: 总文件数={}, 成功={}, 失败={}, 有错误={}",
            totalProcessedFiles, successfulFiles, failedFiles, hasProcessingError);

        return result;
    }

    /**
     * 处理提交数据并与历史错误数据进行比对（从原方法复制，移除事务注解）
     */
    private void processSubmissionDataWithHistoryComparison(List<Map<String, Object>> validRows,
                                                          List<Map<String, Object>> invalidRows,
                                                          Long jobId, UsersDTO usersDTO) {
        // 处理正确数据（每批任务的正确数据都入库，不跳过重复数据）
        List<TransportSchedule> transportSchedules = new ArrayList<>();
        String orgRegionJson = redisService.get("Caict:regionCounty:orgRegion");
        TransportJobNew transportJobNew = transportJobNewService.findById(jobId);

        //获取规则
        List<FsaCheckRuleDTO> fsaCheckRuleDTOList = fsaCheckRuleService.findByOrgType(transportJobNew.getOperatorCode());
        // 将列表转换为 Map，键为 ID，值为姓名
        Map<String, String> tsMap = fsaCheckRuleDTOList.stream()
                .collect(Collectors.toMap(
                        FsaCheckRuleDTO::getNetTs,    // 键：用户 ID
                        FsaCheckRuleDTO::getGenNum   // 值：用户姓名
                        // 处理键冲突的情况（这里假设 ID 唯一，不会冲突）
                ));
        //获取所有区域
        List<RegionDTO> orgRegionList = JSONObject.parseArray(orgRegionJson, RegionDTO.class);

        // 优化：批量处理数据集合
        List<TransportRawBtsDealNew> dealNewList = new ArrayList<>();

        // 优化1：预处理地区分支映射，避免重复查询（改为使用regionCode）
        Map<String, Long> regionToBranchIdMap = new HashMap<>();
        Map<Long, Integer> branchCountMap = new HashMap<>(); // 统计每个分支的数据量
        Set<String> uniqueRegionCodes = validRows.stream()
                .map(row -> transportJobNewWebService.getStringValue(row, "regionCode"))
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // 批量查询或创建地区分支（使用regionCode）
        for (String regionCode : uniqueRegionCodes) {
            // 获取对应的regionName用于显示
            String regionName = transportJobNewWebService.getRegionNameByCode(regionCode, orgRegionList);
            TransportJobBranchNew branch = transportJobBranchNewService.findOrCreateByJobIdAndRegionCode(jobId, regionCode, regionName);
            regionToBranchIdMap.put(regionCode, branch.getId());
            branchCountMap.put(branch.getId(), 0); // 初始化计数
        }

        // 优化2：批量查询历史错误记录，避免逐条查询
        List<TransportRawBtsDealNew> tempEntitiesForHistoryCheck = validRows.stream()
                .map(row -> transportJobNewWebService.convertToTransportRawBtsDealNew(row, jobId))
                .collect(Collectors.toList());

        Map<String, TransportRawBtsDealLogNew> historicalErrorMap = transportJobNewWebService.batchQueryHistoricalErrors(tempEntitiesForHistoryCheck);

        // 处理正确数据
        for (Map<String, Object> row : validRows) {
            String regionCode = transportJobNewWebService.getStringValue(row, "regionCode");

            // 使用预处理的地区分支映射（使用regionCode）
            Long jobBranchId = regionToBranchIdMap.get(regionCode);
            if (jobBranchId == null) {
                // 异常情况处理：如果预处理中没有找到，重新查询
                String county = transportJobNewWebService.getStringValue(row, "county");
                String regionName = transportJobNewWebService.getRegionNameByCode(regionCode, orgRegionList);
                TransportJobBranchNew branch = transportJobBranchNewService.findOrCreateByJobIdAndRegionCode(jobId, regionCode, regionName != null ? regionName : county);
                jobBranchId = branch.getId();
                regionToBranchIdMap.put(regionCode, jobBranchId);
                branchCountMap.put(jobBranchId, 0);
            }

            // 创建临时实体对象用于业务逻辑唯一键比较
            TransportRawBtsDealNew tempEntity = transportJobNewWebService.convertToTransportRawBtsDealNew(row, jobId);

            // 使用批量查询的结果检查历史错误记录
            String businessKey = transportJobNewWebService.buildBusinessLogicKey(tempEntity);
            TransportRawBtsDealLogNew historicalError = historicalErrorMap.get(businessKey);

            // 注意：历史错误记录的删除仍然需要逐条处理，因为需要确保数据一致性
            if (historicalError != null) {
                // 删除历史错误记录（数据修复），即使技术参数发生了变化也能正确匹配
                deleteHistoricalError(historicalError.getId());
            }

            // 设置jobBranchId并收集正确数据
            row.put("jobBranchId", jobBranchId);
            dealNewList.add(transportJobNewWebService.convertToTransportRawBtsDealNew(row, jobId));
            transportSchedules.add(transportJobNewWebService.convertToTransportSchedule(row, transportJobNew, orgRegionList, tsMap));

            // 优化：在内存中累加计数，而不是每次都更新数据库
            branchCountMap.put(jobBranchId, branchCountMap.get(jobBranchId) + 1);
        }

        // 优化3：批量更新地区分支计数
        if (!branchCountMap.isEmpty()) {
            batchUpdateBranchCounts(branchCountMap);
        }

        // 处理错误数据
        if (!invalidRows.isEmpty()) {
            logger.info("=== 开始处理错误数据 ===");
            logger.info("错误数据总数: {}", invalidRows.size());

            processInvalidRowsInBatch(invalidRows, jobId);
        }

        // 批量执行数据库操作
        if (!dealNewList.isEmpty()) {
            batchInsertValidData(dealNewList);
        }

        // 触发后续处理逻辑
        triggerPostProcessing(jobId, transportSchedules, transportJobNew, usersDTO);
    }

    /**
     * 批量处理错误数据
     */
    private void processInvalidRowsInBatch(List<Map<String, Object>> invalidRows, Long jobId) {
        // 批量查询现有错误记录，避免重复插入
        List<TransportRawBtsDealNew> tempEntitiesForQuery = invalidRows.stream()
                .map(row -> transportJobNewWebService.convertToTransportRawBtsDealNew(row, jobId))
                .collect(Collectors.toList());

        Map<String, TransportRawBtsDealLogNew> existingErrorMap = transportJobNewWebService.batchQueryExistingErrors(jobId, tempEntitiesForQuery);
        logger.info("查询到已存在的错误记录数: {}", existingErrorMap.size());

        List<TransportRawBtsDealLogNew> newLogEntityList = new ArrayList<>();
        List<TransportRawBtsDealLogNew> updateLogEntityList = new ArrayList<>();

        for (Map<String, Object> row : invalidRows) {
            @SuppressWarnings("unchecked")
            List<String> errorMessages = (List<String>) row.get("errors");
            String primaryErrorMessage = (errorMessages != null && !errorMessages.isEmpty())
                    ? errorMessages.get(0) : "未知错误";

            // 创建业务逻辑唯一键
            TransportRawBtsDealNew tempEntity = transportJobNewWebService.convertToTransportRawBtsDealNew(row, jobId);
            String businessKey = transportJobNewWebService.buildBusinessLogicKey(tempEntity);

            if (existingErrorMap.containsKey(businessKey)) {
                // 已存在该错误记录，准备更新
                TransportRawBtsDealLogNew existingLog = existingErrorMap.get(businessKey);
                existingLog.setErrorMessage(transportJobNewWebService.sqlSafeString(primaryErrorMessage));
                existingLog.setDealData(transportJobNewWebService.sqlSafeString(JSON.toJSONString(transportJobNewWebService.cleanRowDataForLog(row))));
                existingLog.setCreatedAt(new Date());  // 更新时间字段
                updateLogEntityList.add(existingLog);
            } else {
                // 新的错误记录，准备插入
                TransportRawBtsDealLogNew logEntity = transportJobNewWebService.convertToTransportRawBtsDealLogNew(row, jobId, primaryErrorMessage);
                newLogEntityList.add(logEntity);
            }
        }

        logger.info("准备新增错误记录数: {}", newLogEntityList.size());
        logger.info("准备更新错误记录数: {}", updateLogEntityList.size());

        // 批量处理错误记录
        if (!newLogEntityList.isEmpty()) {
            batchInsertErrorData(newLogEntityList);
        }

        if (!updateLogEntityList.isEmpty()) {
            batchUpdateErrorData(updateLogEntityList);
        }

        logger.info("=== 错误数据处理完成 ===");
    }

    /**
     * 触发后续处理逻辑
     */
    private void triggerPostProcessing(Long jobId, List<TransportSchedule> transportSchedules,
                                     TransportJobNew transportJobNew, UsersDTO usersDTO) {
        //这里先判断APPROVAL_TRANSPORT_JOB各地区数据是否审核完成
        List<String> approvalTransportJobList = approvalTransportJobWebService.findRegionCodeByStatus(
            Arrays.asList("revoked", "done"), usersDTO, null);

        if (approvalTransportJobList.isEmpty()) {
            if ("1".equals(transportJobNew.getApplyType())) {
                //开启线程进行新增/变更比对处理
                CompletableFuture.runAsync(() -> transportJobWebService.checkExpandStation1(
                    String.valueOf(jobId), null, transportSchedules, usersDTO), asyncExecutor);
            } else if ("2".equals(transportJobNew.getApplyType()) || "3".equals(transportJobNew.getApplyType())) {
                //开启线程进行注销处理
                CompletableFuture.runAsync(() -> transportJobWebService.checkExpandStation2(
                    String.valueOf(jobId), null, transportSchedules, usersDTO), asyncExecutor);
            }
        } else {
            //判断前置任务是否完成
            Map<String, List<TransportSchedule>> collect = transportSchedules.stream().collect(Collectors.groupingBy(TransportSchedule::getRegionCode));
            if (!collect.isEmpty()) {
                for (Map.Entry<String, List<TransportSchedule>> entry : collect.entrySet()) {
                    if (!approvalTransportJobList.contains(entry.getKey())) {
                        //开启线程进行新增/变更比对处理
                        if ("1".equals(transportJobNew.getApplyType())) {
                            //开启线程进行新增/变更比对处理
                            CompletableFuture.runAsync(() -> transportJobWebService.checkExpandStation1(
                                String.valueOf(jobId), null, transportSchedules, usersDTO), asyncExecutor);
                        } else if ("2".equals(transportJobNew.getApplyType()) || "3".equals(transportJobNew.getApplyType())) {
                            //开启线程进行延续/注销处理
                            CompletableFuture.runAsync(() -> transportJobWebService.checkExpandStation2(
                                String.valueOf(jobId), null, transportSchedules, usersDTO), asyncExecutor);
                        }
                    } else {
                        //数据插入待办表等待对比
                        insertScheduleData(entry.getValue());
                    }
                }
            }
        }
    }

    /**
     * 删除历史错误记录（使用新事务）
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void deleteHistoricalError(Long errorId) {
        try {
            transportRawBtsDealLogNewService.deleteById(errorId);
            logger.info("删除历史错误记录成功: {}", errorId);
        } catch (Exception e) {
            logger.error("删除历史错误记录失败: {}", errorId, e);
        }
    }

    /**
     * 批量更新分支计数（使用新事务）
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void batchUpdateBranchCounts(Map<Long, Integer> branchCountMap) {
        try {
            transportJobBranchNewService.batchAdjustTotalCount(branchCountMap);
            logger.info("批量更新分支计数成功，数量: {}", branchCountMap.size());
        } catch (Exception e) {
            logger.error("批量更新分支计数失败", e);
            throw e;
        }
    }

    /**
     * 批量插入错误数据（使用新事务）
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void batchInsertErrorData(List<TransportRawBtsDealLogNew> errorDataList) {
        try {
            transportRawBtsDealLogNewService.insertBatchCreateId(errorDataList);
            logger.info("批量插入错误数据成功，数量: {}", errorDataList.size());
        } catch (Exception e) {
            logger.error("批量插入错误数据失败，数量: {}", errorDataList.size(), e);
            throw e;
        }
    }

    /**
     * 批量更新错误数据（使用新事务）
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void batchUpdateErrorData(List<TransportRawBtsDealLogNew> errorDataList) {
        try {
            transportRawBtsDealLogNewService.updateBatch(errorDataList);
            logger.info("批量更新错误数据成功，数量: {}", errorDataList.size());
        } catch (Exception e) {
            logger.error("批量更新错误数据失败，数量: {}", errorDataList.size(), e);
            throw e;
        }
    }

    /**
     * 插入调度数据（使用新事务）
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void insertScheduleData(List<TransportSchedule> scheduleList) {
        try {
            transportScheduleService.insertBatchCreateId(scheduleList);
            logger.info("插入调度数据成功，数量: {}", scheduleList.size());
        } catch (Exception e) {
            logger.error("插入调度数据失败，数量: {}", scheduleList.size(), e);
            throw e;
        }
    }

    /**
     * 更新任务状态（使用新事务）
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void updateJobStatus(Long jobId, String status, String message) {
        try {
            TransportJobNew job = transportJobNewService.findById(jobId);
            if (job != null) {
                job.setStatus(status);
                job.setUpdatedAt(new Date());
                // 注意：这里假设TransportJobNew实体有message字段，如果没有可以考虑添加或使用其他字段
                transportJobNewService.update(job);
                logger.info("任务 {} 状态更新为: {}, 消息: {}", jobId, status, message);
            }
        } catch (Exception e) {
            logger.error("更新任务状态失败: jobId={}, status={}", jobId, status, e);
        }
    }

    /**
     * 更新文件状态（使用新事务）
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void updateFileStatus(Long fileId, Integer status) {
        try {
            transportFileService.updateFileStatus(fileId, status);
            logger.info("文件 {} 状态更新为: {}", fileId, status);
        } catch (Exception e) {
            logger.error("更新文件状态失败: fileId={}, status={}", fileId, status, e);
        }
    }

    /**
     * 批量插入正确数据（使用新事务）
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void batchInsertValidData(List<TransportRawBtsDealNew> dealNewList) {
        try {
            transportRawBtsDealNewService.insertBatchCreateId(dealNewList);
            logger.info("批量插入正确数据完成，数量: {}", dealNewList.size());
        } catch (Exception e) {
            logger.error("批量插入正确数据失败，数量: {}", dealNewList.size(), e);
            throw e;
        }
    }

    /**
     * 保存处理结果（使用新事务）
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void saveProcessingResult(Long jobId, Map<String, Object> result) {
        try {
            // 这里可以将处理结果保存到专门的结果表中
            // 或者更新任务表的相关字段
            logger.info("保存任务 {} 的处理结果: {}", jobId, result);

            // 示例：更新任务的统计信息
            TransportJobNew job = transportJobNewService.findById(jobId);
            if (job != null) {
                // 可以添加字段来保存处理结果统计
                // job.setTotalFiles((Integer) result.get("totalProcessedFiles"));
                // job.setSuccessfulFiles((Integer) result.get("successfulFiles"));
                // job.setFailedFiles((Integer) result.get("failedFiles"));
                transportJobNewService.update(job);
            }
        } catch (Exception e) {
            logger.error("保存处理结果失败: jobId={}", jobId, e);
        }
    }
}
