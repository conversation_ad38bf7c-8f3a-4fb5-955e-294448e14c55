package com.bsm.v4.api.web.service.bussiness.station;

import com.bsm.v4.domain.security.service.business.license.LicenseTService;
import com.bsm.v4.system.model.entity.business.license.RsbtLicenseT;
import com.caictframework.data.service.BasicWebService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/9/11
 */
@Service
public class LicenseTWebService extends BasicWebService {

    @Autowired
    private LicenseTService licenseTService;

    public String save(RsbtLicenseT rsbtLicenseT) {
        return licenseTService.save(rsbtLicenseT).toString();
    }

    public RsbtLicenseT findOne(String guid) {
        return licenseTService.findById(guid);
    }

    /**
     * 插入执照
     *
     * @param licenseList licenseList
     */
    public int insertBatch(List<RsbtLicenseT> licenseList) {
        if (licenseList.size() > 50) {
            int pageNum = licenseList.size() % 50 == 0 ? licenseList.size() / 50 : licenseList.size() / 50 + 1;
            for (int i = 1; i <= pageNum; i++) {
                List<RsbtLicenseT> licenses;
                if (i != pageNum) {
                    licenses = licenseList.subList((i - 1) * 50, i * 50);
                } else {
                    licenses = licenseList.subList((i - 1) * 50, licenseList.size());
                }
                licenseTService.insertBatch(licenses);
            }
        } else {
            licenseTService.insertBatch(licenseList);
        }
        return 0;
    }

    /**
     * 批量修改
     */
    public int updateBatch(List<RsbtLicenseT> licenseList) {
        return licenseTService.updateBatch(licenseList);
    }
}
