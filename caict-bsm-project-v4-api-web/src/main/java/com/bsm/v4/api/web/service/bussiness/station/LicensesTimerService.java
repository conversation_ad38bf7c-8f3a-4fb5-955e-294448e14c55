package com.bsm.v4.api.web.service.bussiness.station;

import com.bsm.v4.domain.security.service.business.license.LicenseService;
import com.bsm.v4.system.model.contrust.license.LicenseStateConst;
import com.bsm.v4.system.model.dto.business.license.LicenseDTO;
import com.bsm.v4.system.model.entity.business.license.RsbtLicenseT;
import com.caictframework.data.service.BasicWebService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/15 15:50
 */
@Service
public class LicensesTimerService extends BasicWebService {

    @Autowired
    private LicenseService licenseService;

    @Autowired
    private LicenseTWebService licenseTWebService;

    /**
     * 1.已发
     * 2.过期
     * 3.停用
     * 4.注销
     * 5.即将过期
     */
    //判断执照是否到期
    public void IsExpiredLicenses() {
        // 查询所有未过期或即将过期的执照
        List<LicenseDTO> licenseDTOList = licenseService.findNotExpiredLicenses(1L, 5L);
        Date currentDate = new Date();

        List<RsbtLicenseT> rsbtLicenseTList = licenseDTOList.stream().map(license -> {
                    RsbtLicenseT rsbtLicenseT = new RsbtLicenseT();
                    rsbtLicenseT.setGuid(license.getGuid());
                    if(license.getLicenseDateE() != null ) {
                        Date licenseDateE = license.getLicenseDateE();
                        long daysUntilExpiration = (licenseDateE.getTime() - currentDate.getTime()) / (1000 * 60 * 60 * 24);

                        //如果时间差小于0 则设置状态为过期
                        if (daysUntilExpiration <= 0) {
                            rsbtLicenseT.setLicenseState(LicenseStateConst.LICENSE_EXPIRE);
                        }
                        //如果时间差小于7天 则设置状态为即将过期
                        else if (daysUntilExpiration <= 7) {
                            rsbtLicenseT.setLicenseState(LicenseStateConst.LICENSE_WILL_EXPIRE);
                        }
                        //否则 设置为已发
                        else {
                            rsbtLicenseT.setLicenseState(LicenseStateConst.LICENSE_REGULAR);
                        }
                    }
                    return rsbtLicenseT;
                })
                .collect(Collectors.toList());
        // 批量更新 RsbtLicenseT 表
        if (!rsbtLicenseTList.isEmpty()) {
            licenseTWebService.updateBatch(rsbtLicenseTList);
        }
    }
}
