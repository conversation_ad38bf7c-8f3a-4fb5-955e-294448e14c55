package com.bsm.v4.api.web.controller.business.transferNew;

import com.alibaba.fastjson.JSONObject;
import com.bsm.v4.api.web.service.bussiness.transferNew.TransportJobNewWebService;
import com.bsm.v4.api.web.service.security.AuthWebService;
import com.bsm.v4.system.model.vo.business.transferNew.DataDetailVO;
import com.bsm.v4.system.model.vo.business.transferNew.TransportJobNewSearchVO;
import com.bsm.v4.system.model.vo.business.transferNew.SubmitJobVO;
import com.caictframework.data.controller.BasicController;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * 传输任务表Controller
 */
@RestController
@RequestMapping(value = "/apiWeb/transferNew/transportJobNew")
@Api(value = "任务管理接口", tags = "任务管理接口")
public class   TransportJobNewController extends BasicController {

    @Autowired
    private TransportJobNewWebService transportJobNewWebService;

    @Autowired
    private AuthWebService authWebService;


    @ApiOperation(value = "提交任务", notes = "提交任务进行数据校验并绑定上传文件")
    @PostMapping(value = "/submitJob")
    public JSONObject submitJob(
            @ApiParam(value = "提交任务请求对象", required = true) @RequestBody SubmitJobVO submitJobVO,
            @ApiParam(value = "用户token", required = true) @RequestHeader("token") String token) {

        return this.basicReturnJson(submitJobVO, TransportJobNewWebService.class,
                (vo, service) -> service.submitJob(vo, token));
    }

    @ApiOperation(value = "暂存任务", notes = "暂存任务，只进行任务和文件的关联保存，不进行数据校验")
    @PostMapping(value = "/draftJob")
    public JSONObject draftJob(
            @ApiParam(value = "暂存任务请求对象", required = true) @RequestBody SubmitJobVO submitJobVO,
            @ApiParam(value = "用户token", required = true) @RequestHeader("token") String token) {

        return this.basicReturnJson(submitJobVO, TransportJobNewWebService.class,
                (vo, service) -> service.draftJob(vo, token));
    }

    @ApiOperation(value = "查询暂存任务列表", notes = "查询当前用户的暂存任务列表")
    @PostMapping(value = "/findDraftJobs")
    public JSONObject findDraftJobs(
            @ApiParam(value = "查询条件", required = true) @RequestBody TransportJobNewSearchVO dto,
            @ApiParam(value = "用户token", required = true) @RequestHeader("token") String token) {
        return this.basicReturnJson(dto, TransportJobNewWebService.class,
                (condition, service) -> service.findDraftJobs(condition, token));
    }

    @ApiOperation(value = "删除暂存任务", notes = "删除指定的暂存任务（只能删除暂存状态的任务）")
    @DeleteMapping(value = "/deleteDraftJob")
    public JSONObject deleteDraftJob(
            @ApiParam(value = "任务ID", required = true) @RequestParam("jobId") Long jobId) {
        return this.basicReturnJson(jobId, TransportJobNewWebService.class,
                (id, service) -> service.deleteDraftJob(id));
    }


    @ApiOperation(value = "分页查询任务列表", notes = "分页查询任务列表，支持多条件筛选")
    @PostMapping(value = "/findJobs")
    public JSONObject findJobs(
            @ApiParam(value = "查询条件", required = true) @RequestBody TransportJobNewSearchVO dto,
            @ApiParam(value = "用户token", required = true) @RequestHeader("token") String token) {
        return this.basicReturnJson(dto, TransportJobNewWebService.class,
                (condition, service) -> service.findJobsWithConditions(condition, token));
    }

    @ApiOperation(value = "任务详情查询", notes = "查询指定任务的详细信息，包括关联文件和数据统计")
    @GetMapping(value = "/getJobDetail")
    public JSONObject getJobDetail(
            @ApiParam(value = "任务ID", required = true) @RequestParam("jobId") Long jobId) {
        return this.basicReturnJson(jobId, TransportJobNewWebService.class,
                (id, service) -> service.getJobDetail(id));
    }


    @ApiOperation(value = "任务状态统计", notes = "统计各个状态的任务数量")
    @GetMapping(value = "/getJobStatusStatistics")
    public JSONObject getJobStatusStatistics() {
        return this.basicReturnJson("", TransportJobNewWebService.class,
                (param, service) -> service.getJobStatusStatistics());
    }

    @ApiOperation(value = "根据创建人查询任务", notes = "查询指定创建人的所有任务")
    @GetMapping(value = "/getJobsByCreatedBy")
    public JSONObject getJobsByCreatedBy(
            @ApiParam(value = "创建人", required = true) @RequestParam("createdBy") String createdBy) {
        return this.basicReturnJson(createdBy, TransportJobNewWebService.class,
                (creator, service) -> service.getJobsByCreatedBy(creator));
    }

    @ApiOperation(value = "根据任务ID分页查询数据详情（错误数据、正确数据）", notes = "根据任务ID分页查询数据详情（错误数据、正确数据）")
    @PostMapping(value = "/getDataDetailByJobId")
    public JSONObject getDataDetailByJobId(
            @ApiParam(value = "查询条件", required = true) @RequestBody DataDetailVO dto) {
        return this.basicReturnJson(dto, TransportJobNewWebService.class,
                (queryDto, service) -> service.getDataDetailByJobId(queryDto));
    }

    @ApiOperation(value = "根据任务ID分页查询错误数据详情", notes = "专门用于分页查询指定任务的错误数据详情，包括错误信息、字段名、原值等")
    @PostMapping(value = "/getErrorDataDetailByJobId")
    public JSONObject getErrorDataDetailByJobId(
            @ApiParam(value = "查询条件", required = true) @RequestBody DataDetailVO dto) {
        return this.basicReturnJson(dto, TransportJobNewWebService.class,
                (queryDto, service) -> service.getErrorDataDetailByJobId(queryDto));
    }

    @ApiOperation(value = "根据任务ID下载数据详情（错误数据、正确数据）", notes = "根据任务ID下载数据详情（错误数据、正确数据）")
    @GetMapping(value = "/downloadDataDetailByJobId")
    public JSONObject downloadDataDetailByJobId(
            @ApiParam(value = "任务ID", required = true) @RequestParam("jobId") Long jobId) {
        return this.basicReturnJson(jobId, TransportJobNewWebService.class,
                (id, service) -> service.downloadDataDetailByJobId(id));
    }

    @ApiOperation(value = "分页查看审核记录列表", notes = "分页查看审核记录列表接口")
    @RequestMapping(value = "/findAllPage", method = RequestMethod.POST)
    public JSONObject findAllPage(@RequestBody TransportJobNewSearchVO dto, @RequestHeader("token")String token){
        return this.basicReturnJson(dto, TransportJobNewWebService.class,
                (model, service) -> service.findAllPage(token,dto));

    }

    @ApiOperation(value = "查看审核记录详情", notes = "查看审核记录详情接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "jobId",value = "任务id",paramType = "path",dataType = "String")
    })
    @RequestMapping(value = "/detail/{jobId}", method = RequestMethod.GET)
    public JSONObject detail(@PathVariable("jobId")Long jobId)
    {
        return this.basicReturnJson(jobId, TransportJobNewWebService.class,
                (model, service) -> service.detail(jobId));
    }

    @ApiOperation(value = "查询任务处理状态", notes = "查询任务的处理状态和进度，用于异步处理时前端轮询")
    @GetMapping(value = "/getJobStatus")
    public JSONObject getJobStatus(
            @ApiParam(value = "任务ID", required = true) @RequestParam("jobId") Long jobId) {
        return this.basicReturnJson(jobId, TransportJobNewWebService.class,
                (id, service) -> service.getJobStatus(id));
    }

}
