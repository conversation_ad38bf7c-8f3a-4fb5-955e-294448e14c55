package com.bsm.v4.api.web.timer;

import com.bsm.v4.api.web.service.bussiness.station.LicensesTimerService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/15 15:46
 */

@Component
@EnableScheduling
public class IsExpiredLicensesTimer {
    private static final Logger LOG = LoggerFactory.getLogger(IsExpiredLicensesTimer.class);

    @Autowired
    private LicensesTimerService licensesTimerService;

    /**
     *  每天凌晨2点执行一次判断执照是否过期
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void checkExpiredLicenses() {
        LOG.info("开始执行判断执照到期任务");
        licensesTimerService.IsExpiredLicenses();
    }
}
