package com.bsm.v4.domain.security.mapper.security;

import com.bsm.v4.system.model.dto.security.OrgDTO;
import com.bsm.v4.system.model.dto.security.UsersDTO;
import com.bsm.v4.system.model.entity.security.RsbtOrgUsers;
import com.caictframework.data.mapper.BasicMapper;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by dengsy on 2019-05-06.
 */
@Repository
public interface RsbtOrgUsersMapper extends BasicMapper<RsbtOrgUsers> {

    /**
     * 根据组织机构查询全部用户
     */
//    @Select("select SYS_USERS.* from RSBT_ORG_USERS,SYS_USERS where RSBT_ORG_USERS.USERS_ID = SYS_USERS.ID and RSBT_ORG_USERS.ORG_GUID = #{orgGuid}")
//    List<UsersDTO> findAllUsersByOrg(@Param("orgGuid") String orgGuid);
    @Select("select SYS_USERS.* from RSBT_ORG_USERS,SYS_USERS where RSBT_ORG_USERS.USERS_ID = SYS_USERS.ID and RSBT_ORG_USERS.ORG_GUID = #{orgGuid}")
    List<UsersDTO> findAllUsersByOrg(@Param("orgGuid") Long orgGuid);

    /**
     * 根据用户删除所有
     */
    @Delete("DELETE FROM RSBT_ORG_USERS WHERE USERS_ID = #{usersId}")
    int deleteAllByUsersId(@Param("usersId") Long usersId);

    /**
     * 根据userId 查组织机构
     */
    @Select("SELECT RSBT_ORG.ORG_CODE,RSBT_ORG_APPENDIX.GUID,RSBT_ORG_APPENDIX.TYPE FROM SYS_USERS,RSBT_ORG_USERS,RSBT_ORG,RSBT_ORG_APPENDIX " +
            "WHERE SYS_USERS.ID = RSBT_ORG_USERS.USERS_ID " +
            "AND RSBT_ORG.GUID = RSBT_ORG_APPENDIX.GUID " +
            "AND RSBT_ORG_USERS.ORG_GUID = RSBT_ORG_APPENDIX.GUID " +
            "AND USERS_ID = #{userId} " +
            "AND ROWNUM = 1 ")
    OrgDTO findOrgGuidByUserId(@Param("userId") Long userId);

    /**
     * 根据组织机构删除所有
     */
    @Delete("DELETE FROM RSBT_ORG_USERS WHERE ORG_GUID = #{orgGuid}")
    int deleteAllByOrg(@Param("orgGuid") Long orgGuid);


//    @Select("select o.* from SYS_ORG o left join SYS_ORG_USERS ou on o.id = ou.ORG_GUID where ou.USERS_ID = #{userId} ")
//    OrgDTO findRsbtOrgByUserId(@Param("userId") String userId);

    @Select("select o.*, a.type, ou.USERS_ID " +
            "from RSBT_ORG o, RSBT_ORG_APPENDIX a,RSBT_ORG_USERS ou " +
            "where o.GUID = a.GUID " +
            "and o.GUID = ou.ORG_GUID " +
            "and ou.USERS_ID = #{userId}  LIMIT 1 ")
    OrgDTO findRsbtOrgByUserId(@Param("userId") Long userId);
}
