package com.bsm.v4.domain.security.mapper.business.transferNew;

import com.bsm.v4.system.model.vo.business.transferNew.TransportJobBranchNewNewVO;
import com.bsm.v4.system.model.dto.business.transfer_in.SchedualDataDTO;
import com.bsm.v4.system.model.entity.business.transferNew.TransportJobBranchNew;
import com.caictframework.data.mapper.BasicMapper;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 按地市拆分的子任务表Mapper
 */
@Repository
public interface TransportJobBranchNewMapper extends BasicMapper<TransportJobBranchNew> {

    /**
     * 调整统计数据（带下限保护）
     * 使用GREATEST函数确保数值不会小于0
     */
    @Update("UPDATE TRANSPORT_JOB_BRANCH_NEW SET " +
            "total_count = GREATEST(0, total_count + #{totalCountDelta}), " +
            "error_count = GREATEST(0, error_count + #{errorCountDelta}), " +
            "updated_at = NOW() " +
            "WHERE id = #{id}")
    int adjustStatistics(@Param("id") Long id,
                         @Param("totalCountDelta") int totalCountDelta,
                         @Param("errorCountDelta") int errorCountDelta);

    /**
     * 根据jobId和regionName查找（兼容旧方法）
     */
    @Select("SELECT * FROM TRANSPORT_JOB_BRANCH_NEW WHERE job_id = #{jobId} AND region_name = #{regionName} LIMIT 1")
    TransportJobBranchNew findByJobIdAndRegion(@Param("jobId") Long jobId, @Param("regionName") String regionName);

    /**
     * 根据jobId和regionCode查找（新方法，推荐使用）
     */
    @Select("SELECT * FROM TRANSPORT_JOB_BRANCH_NEW WHERE job_id = #{jobId} AND region_code = #{regionCode} LIMIT 1")
    TransportJobBranchNew findByJobIdAndRegionCode(@Param("jobId") Long jobId, @Param("regionCode") String regionCode);

    /**
     * 根据任务ID删除所有分支记录
     */
    @Delete("DELETE FROM TRANSPORT_JOB_BRANCH_NEW WHERE job_id = #{jobId}")
    int deleteByJobId(@Param("jobId") Long jobId);


    /**
     * 根据任务ID更新分支状态
     */
    @Update("UPDATE TRANSPORT_JOB_BRANCH_NEW SET status = #{status} where job_id = #{jobId}")
    int updateByJobId(@Param("jobId") Long jobId, @Param("status") String status);

    /**
     * 根据任务、区域查询
     */
    @Select("select t.*  FROM TRANSPORT_JOB_BRANCH_NEW t, SYS_REGION r " +
            "WHERE t.region_name = r.NAME AND r.CODE = #{regionCode} " +
            "and (t.job_id = #{jobId,jdbcType=VARCHAR} or #{jobId,jdbcType=VARCHAR} is null) ")
    TransportJobBranchNewNewVO findOneByJobUserTypeGen(@Param("jobId") String jobId, @Param("regionCode") String regionCode);

    /**
     * 根据任务ID查询所有分支
     */
    @Select("SELECT * FROM TRANSPORT_JOB_BRANCH_NEW WHERE job_id = #{jobId}")
    List<TransportJobBranchNew> findByJobId(@Param("jobId") Long jobId);

}
