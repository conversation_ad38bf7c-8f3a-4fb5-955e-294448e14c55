package com.bsm.v4.domain.security.mapper.business.license;

import com.bsm.v4.system.model.dto.business.license.LicenseDTO;
import com.bsm.v4.system.model.dto.business.license.LicensePdfDTO;
import com.bsm.v4.system.model.dto.business.license.LicenseStatisticDTO;
import com.bsm.v4.system.model.dto.business.station.ISectionDTO;
import com.bsm.v4.system.model.dto.security.UsersDTO;
import com.bsm.v4.system.model.entity.business.license.RsbtLicense;
import com.bsm.v4.system.model.vo.business.License.LicenseVO;
import com.caictframework.data.mapper.BasicMapper;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/22
 */
@Repository
public interface LicenseMapper extends BasicMapper<RsbtLicense> {

    /**
     * 条件查询总数
     */
    @Select("select count(*) from BSM_STATION_LICENSE " +
            "LEFT JOIN RSBT_STATION ON BSM_STATION_LICENSE.STATION_GUID = RSBT_STATION.STATION_GUID " +
            "LEFT JOIN RSBT_ORG ON RSBT_STATION.ORG_GUID = RSBT_ORG.ORG_GUID " +
            "LEFT JOIN FSA_USERS ON RSBT_ORG.ORG_USER = FSA_USERS.ID " +
            "LEFT JOIN BSM_APPLYTABLE_STATION ON BSM_APPLYTABLE_STATION.STATION_GUID = RSBT_STATION.STATION_GUID " +
            "LEFT JOIN BSM_APPLYTABLE ON BSM_APPLYTABLE.GUID = BSM_APPLYTABLE_STATION.APPLYTABLE_GUID " +
            "where BSM_STATION_LICENSE.IS_DELETED = 0 and BSM_STATION_LICENSE.LICENSE_CODE is not null " +
            "and (FSA_USERS.TYPE = #{licenseDTO.userType,jdbcType=VARCHAR} or #{licenseDTO.userType,jdbcType=VARCHAR} is null) " +
            "and (RSBT_ORG.ORG_NAME = #{licenseDTO.orgName,jdbcType=VARCHAR} or #{licenseDTO.orgName,jdbcType=VARCHAR} is null) " +
            "and (RSBT_STATION.STAT_NAME like concat(concat('%',#{licenseDTO.stationName,jdbcType=VARCHAR}),'%') or #{licenseDTO.stationName,jdbcType=VARCHAR} is null) " +
            "and (BSM_STATION_LICENSE.LICENSE_END_DATE <= #{licenseDTO.stationStartDate,jdbcType=VARCHAR} or #{licenseDTO.stationStartDate,jdbcType=VARCHAR} is null) " +
            "and (BSM_STATION_LICENSE.LICENSE_CODE like concat(concat('%',#{licenseDTO.licenseCode,jdbcType=VARCHAR}),'%') or #{licenseDTO.licenseCode,jdbcType=VARCHAR} is null) " +
            "and (RSBT_STATION.ST_C_CODE like concat(concat('%',#{licenseDTO.stationCode,jdbcType=VARCHAR}),'%') or #{licenseDTO.stationCode,jdbcType=VARCHAR} is null) " +
            "and (BSM_STATION_LICENSE.LICENSE_STATE = #{licenseDTO.licenseState,jdbcType=VARCHAR} or #{licenseDTO.licenseState,jdbcType=VARCHAR} is null) " +
            "and (RSBT_STATION.NET_TS = #{licenseDTO.netType,jdbcType=VARCHAR} or #{licenseDTO.netType,jdbcType=VARCHAR} is null) ")
    int selectCountWhere(@Param("licenseDTO") LicenseDTO licenseDTO);

    /**
     * 分页条件查询 省级运营商
     *
     * @param vo LicenseVO
     * @param usersDTO   usersDTO
     * @return list
     */
    @Select("<script>" +
            "select RSBT_LICENSE.*,RSBT_STATION.GUID as stationGuid,RSBT_STATION.STAT_NAME AS stationName,RSBT_STATION_T.ST_C_CODE AS stationCode,RSBT_STATION.STAT_ADDR AS location, " +
            "RSBT_NET.NET_TS AS netType,RSBT_STATION.STAT_LG AS longitude,RSBT_STATION.STAT_LA AS latitude,RSBT_STATION.APP_CODE AS applyTableCode,RSBT_ORG.ORG_NAME AS orgName,RSBT_LICENSE_T.LICENSE_STATE AS LICENSESTATE  " +
            "from RSBT_STATION " +
            "INNER JOIN RSBT_LICENSE ON RSBT_LICENSE.STATION_GUID = RSBT_STATION.GUID " +
            "INNER JOIN RSBT_STATION_T ON RSBT_LICENSE.STATION_GUID = RSBT_STATION_T.GUID " +
            "LEFT JOIN RSBT_NET ON RSBT_STATION.NET_GUID = RSBT_NET.GUID " +
            "LEFT JOIN RSBT_ORG ON RSBT_NET.ORG_GUID = RSBT_ORG.GUID " +
            "LEFT JOIN RSBT_ORG_APPENDIX ON RSBT_ORG.GUID = RSBT_ORG_APPENDIX.GUID " +
            "LEFT JOIN RSBT_APPLY ON RSBT_NET.GUID = RSBT_APPLY.NET_GUID " +
            "INNER JOIN RSBT_LICENSE_T ON RSBT_LICENSE.GUID = RSBT_LICENSE_T.GUID " +
            "where 1 = 1 " +
            "<if test='vo.orgName != null and vo.orgName != \"\"'>" +
            "   AND RSBT_ORG.ORG_NAME = #{vo.orgName,jdbcType=VARCHAR} " +
            "</if>" +
            "<if test='vo.stationName != null and vo.stationName != \"\"'>" +
            "   AND RSBT_STATION.STAT_NAME LIKE CONCAT('%', #{vo.stationName,jdbcType=VARCHAR}, '%') " +
            "</if>" +
           /* "and (RSBT_ORG.ORG_NAME = #{vo.orgName,jdbcType=VARCHAR} or #{vo.orgName,jdbcType=VARCHAR} is null) " +
            "and (RSBT_STATION.STAT_NAME like concat(concat('%',#{vo.stationName,jdbcType=VARCHAR}),'%') or #{vo.stationName,jdbcType=VARCHAR} is null) " +*/
            "and  RSBT_STATION.STAT_AREA_CODE in (" +
            "    WITH RECURSIVE RegionTree AS (" +
            "        SELECT ID, PARENT_ID, CODE FROM SYS_REGION WHERE ID = #{usersDTO.regionId,jdbcType=VARCHAR}" +
            "        UNION ALL" +
            "        SELECT r.ID, r.PARENT_ID, r.CODE FROM SYS_REGION r INNER JOIN RegionTree t ON r.PARENT_ID = t.ID" +
            "    )" +
            "    SELECT CODE FROM RegionTree" +
            " )" +
            "<if test='vo.startDate != null'>" +
            "   AND RSBT_LICENSE.License_Date_E &gt;= #{vo.startDate, jdbcType=DATE} " +
            "</if>" +
            "<if test='vo.endDate != null'>" +
            "   AND RSBT_LICENSE.License_Date_E &lt;= #{vo.endDate, jdbcType=DATE} " +
            "</if>" +
            "<if test='vo.licenseCode != null and vo.licenseCode != \"\"'>" +
            "   AND RSBT_LICENSE.LICENSE_CODE LIKE CONCAT('%', #{vo.licenseCode,jdbcType=VARCHAR}, '%') " +
            "</if>" +
            "<if test='vo.stationCode != null and vo.stationCode != \"\"'>" +
            "   AND RSBT_STATION_T.ST_C_CODE LIKE CONCAT('%', #{vo.stationCode,jdbcType=VARCHAR}, '%') " +
            "</if>" +
            "<if test='vo.licenseState != null and vo.licenseState != \"\"'>" +
            "   AND RSBT_LICENSE_T.LICENSE_STATE = #{vo.licenseState,jdbcType=VARCHAR} " +
            "</if>" +
            "<if test='vo.netType != null and vo.netType != \"\"'>" +
            "   AND RSBT_NET.NET_TS = #{vo.netType,jdbcType=VARCHAR} " +
            "</if>" +
            "<if test='usersDTO.type != null and usersDTO.type != \"\"'>" +
            "   AND RSBT_ORG_APPENDIX.TYPE = #{usersDTO.type,jdbcType=VARCHAR} " +
            "</if>" +
            /*"and (RSBT_LICENSE.License_Date_E <= #{vo.stationStartDate,jdbcType=DATE} or #{vo.stationStartDate,jdbcType=DATE} is null) " +
            "and (RSBT_LICENSE.LICENSE_CODE like concat(concat('%',#{vo.licenseCode,jdbcType=VARCHAR}),'%') or #{vo.licenseCode,jdbcType=VARCHAR} is null) " +
            "and (RSBT_STATION_T.ST_C_CODE like concat(concat('%',#{vo.stationCode,jdbcType=VARCHAR}),'%') or #{vo.stationCode,jdbcType=VARCHAR} is null) " +
            "and (RSBT_LICENSE_T.LICENSE_STATE = #{vo.licenseState,jdbcType=VARCHAR} or #{vo.licenseState,jdbcType=VARCHAR} is null) " +
            "and (RSBT_NET.NET_TS = #{vo.netType,jdbcType=VARCHAR} or #{vo.netType,jdbcType=VARCHAR} is null) " +
            "and (RSBT_ORG_APPENDIX.TYPE = #{usersDTO.type,jdbcType=VARCHAR} or #{usersDTO.type,jdbcType=VARCHAR} is null) " +*/
            "order by RSBT_LICENSE.LICENSE_CODE " +
            "</script>")
    List<LicenseDTO> findAllPageByWhere1(@Param("vo") LicenseVO vo, @Param("usersDTO") UsersDTO usersDTO);

    /**
     * 分页条件查询 地市运营商
     *
     * @param vo LicenseVO
     * @param usersDTO   usersDTO
     * @return list
     */
    @Select("<script>" +
            "select RSBT_LICENSE.*,RSBT_STATION.GUID as stationGuid,RSBT_STATION.STAT_NAME AS stationName,RSBT_STATION_T.ST_C_CODE AS stationCode,RSBT_STATION.STAT_ADDR AS location, " +
            "RSBT_NET.NET_TS AS netType,RSBT_STATION.STAT_LG AS longitude,RSBT_STATION.STAT_LA AS latitude,RSBT_STATION.APP_CODE AS applyTableCode,RSBT_ORG.ORG_NAME AS orgName,RSBT_LICENSE_T.LICENSE_STATE AS LICENSESTATE  " +
            "from RSBT_STATION " +
            "INNER JOIN RSBT_LICENSE ON RSBT_LICENSE.STATION_GUID = RSBT_STATION.GUID " +
            "INNER JOIN RSBT_STATION_T ON RSBT_LICENSE.STATION_GUID = RSBT_STATION_T.GUID " +
            "LEFT JOIN RSBT_NET ON RSBT_STATION.NET_GUID = RSBT_NET.GUID " +
            "LEFT JOIN RSBT_ORG ON RSBT_NET.ORG_GUID = RSBT_ORG.GUID " +
            "LEFT JOIN RSBT_ORG_APPENDIX ON RSBT_ORG.GUID = RSBT_ORG_APPENDIX.GUID " +
            "LEFT JOIN RSBT_APPLY ON RSBT_NET.GUID = RSBT_APPLY.NET_GUID " +
            "INNER JOIN RSBT_LICENSE_T ON RSBT_LICENSE.GUID = RSBT_LICENSE_T.GUID " +
            "where 1 = 1 " +
            "<if test='vo.orgName != null and vo.orgName != \"\"'>" +
            "   AND RSBT_ORG.ORG_NAME = #{vo.orgName,jdbcType=VARCHAR} " +
            "</if>" +
            "<if test='vo.stationName != null and vo.stationName != \"\"'>" +
            "   AND RSBT_STATION.STAT_NAME LIKE CONCAT('%', #{vo.stationName,jdbcType=VARCHAR}, '%') " +
            "</if>" +
           /* "and (RSBT_ORG.ORG_NAME = #{vo.orgName,jdbcType=VARCHAR} or #{vo.orgName,jdbcType=VARCHAR} is null) " +
            "and (RSBT_STATION.STAT_NAME like concat(concat('%',#{vo.stationName,jdbcType=VARCHAR}),'%') or #{vo.stationName,jdbcType=VARCHAR} is null) " +*/
            "and  RSBT_STATION.STAT_AREA_CODE in (" +
            "    WITH RECURSIVE RegionTree AS (" +
            "        SELECT ID, PARENT_ID, CODE FROM SYS_REGION WHERE ID = #{usersDTO.regionId,jdbcType=VARCHAR}" +
            "        UNION ALL" +
            "        SELECT r.ID, r.PARENT_ID, r.CODE FROM SYS_REGION r INNER JOIN RegionTree t ON r.PARENT_ID = t.ID" +
            "    )" +
            "    SELECT CODE FROM RegionTree" +
            " )" +
            "<if test='vo.startDate != null'>" +
            "   AND RSBT_LICENSE.License_Date_E &gt;= #{vo.startDate, jdbcType=DATE} " +
            "</if>" +
            "<if test='vo.endDate != null'>" +
            "   AND RSBT_LICENSE.License_Date_E &lt;= #{vo.endDate, jdbcType=DATE} " +
            "</if>" +
            "<if test='vo.licenseCode != null and vo.licenseCode != \"\"'>" +
            "   AND RSBT_LICENSE.LICENSE_CODE LIKE CONCAT('%', #{vo.licenseCode,jdbcType=VARCHAR}, '%') " +
            "</if>" +
            "<if test='vo.stationCode != null and vo.stationCode != \"\"'>" +
            "   AND RSBT_STATION_T.ST_C_CODE LIKE CONCAT('%', #{vo.stationCode,jdbcType=VARCHAR}, '%') " +
            "</if>" +
            "<if test='vo.licenseState != null and vo.licenseState != \"\"'>" +
            "   AND RSBT_LICENSE_T.LICENSE_STATE = #{vo.licenseState,jdbcType=VARCHAR} " +
            "</if>" +
            "<if test='vo.netType != null and vo.netType != \"\"'>" +
            "   AND RSBT_NET.NET_TS = #{vo.netType,jdbcType=VARCHAR} " +
            "</if>" +
            "<if test='usersDTO.type != null and usersDTO.type != \"\"'>" +
            "   AND RSBT_ORG_APPENDIX.TYPE = #{usersDTO.type,jdbcType=VARCHAR} " +
            "</if>" +
           /* "and (RSBT_LICENSE.License_Date_E <= #{vo.stationStartDate,jdbcType=DATE} or #{vo.stationStartDate,jdbcType=DATE} is null) " +
            "and (RSBT_LICENSE.LICENSE_CODE like concat(concat('%',#{vo.licenseCode,jdbcType=VARCHAR}),'%') or #{vo.licenseCode,jdbcType=VARCHAR} is null) " +
            "and (RSBT_STATION_T.ST_C_CODE like concat(concat('%',#{vo.stationCode,jdbcType=VARCHAR}),'%') or #{vo.stationCode,jdbcType=VARCHAR} is null) " +
            "and (RSBT_LICENSE_T.LICENSE_STATE = #{vo.licenseState,jdbcType=VARCHAR} or #{vo.licenseState,jdbcType=VARCHAR} is null) " +
            "and (RSBT_NET.NET_TS = #{vo.netType,jdbcType=VARCHAR} or #{vo.netType,jdbcType=VARCHAR} is null) " +
            "and (RSBT_ORG_APPENDIX.TYPE = #{usersDTO.type,jdbcType=VARCHAR} or #{usersDTO.type,jdbcType=VARCHAR} is null) " +*/
            "order by RSBT_LICENSE.LICENSE_CODE " +
            "</script>")
    List<LicenseDTO> findAllPageByWhere2(@Param("vo") LicenseVO vo, @Param("usersDTO") UsersDTO usersDTO);

    /**
     * 分页条件查询 wuwei
     *
     * @param vo LicenseVO
     * @param usersDTO   usersDTO
     * @return list
     */
    @Select("select * from (select RSBT_STATION_BAK.STATION_GUID as stationGuid, RSBT_LICENSE.GUID as GUID,RSBT_STATION_BAK.STAT_NAME AS stationName,RSBT_STATION_T.ST_C_CODE AS stationCode," +
            " RSBT_STATION_BAK.STAT_ADDR AS location, RSBT_STATION_T.ST_D_TEC_TYPE as netType,RSBT_STATION_BAK.STAT_LG AS longitude,RSBT_LICENSE.LICENSE_DATE_B," +
            " RSBT_STATION_BAK.STAT_LA AS latitude,RSBT_STATION.APP_CODE AS applyTableCode,RSBT_ORG.ORG_NAME AS orgName,RSBT_LICENSE.LICENSE_DATE_E,RSBT_LICENSE.LICENSE_CODE," +
            " RSBT_LICENSE_T.LICENSE_STATE AS LICENSESTATE, row_number() over (partition by RSBT_STATION_BAK.ST_C_CODE order by RSBT_STATION_BAK.BAK_DATE desc) rn" +
            " from RSBT_STATION_BAK INNER JOIN RSBT_LICENSE ON RSBT_LICENSE.STATION_GUID = RSBT_STATION_BAK.STATION_GUID" +
            " INNER JOIN RSBT_LICENSE_T ON RSBT_LICENSE.GUID = RSBT_LICENSE_T.GUID INNER JOIN RSBT_STATION ON RSBT_STATION.GUID = RSBT_STATION_BAK.STATION_GUID" +
            " INNER JOIN RSBT_STATION_T ON RSBT_LICENSE.STATION_GUID = RSBT_STATION_T.GUID LEFT JOIN RSBT_ORG_APPENDIX ON RSBT_STATION_BAK.ORG_TYPE = RSBT_ORG_APPENDIX.TYPE" +
            " LEFT JOIN RSBT_ORG ON RSBT_ORG_APPENDIX.GUID = RSBT_ORG.GUID where RSBT_ORG_APPENDIX.STATUS = 1 and RSBT_STATION_BAK.IS_SHOW = 1" +
            " and (RSBT_ORG.ORG_NAME = #{vo.orgName,jdbcType=VARCHAR} or #{vo.orgName,jdbcType=VARCHAR} is null)" +
            " and (RSBT_STATION_BAK.STAT_NAME like concat(concat('%',#{vo.stationName,jdbcType=VARCHAR}),'%') or #{vo.stationName,jdbcType=VARCHAR} is null)" +
            " and (RSBT_STATION.STAT_AREA_CODE = #{vo.licenseCounty,jdbcType=VARCHAR} or #{vo.licenseCounty,jdbcType=VARCHAR} is null)" +
            " and (RSBT_LICENSE.License_Date_E <= #{vo.stationStartDate,jdbcType=DATE} or #{vo.stationStartDate,jdbcType=DATE} is null)" +
            " and (RSBT_LICENSE.LICENSE_CODE like concat(concat('%',#{vo.licenseCode,jdbcType=VARCHAR}),'%') or #{vo.licenseCode,jdbcType=VARCHAR} is null)" +
            " and (RSBT_STATION_T.ST_C_CODE like concat(concat('%',#{vo.stationCode,jdbcType=VARCHAR}),'%') or #{vo.stationCode,jdbcType=VARCHAR} is null)" +
            " and (RSBT_LICENSE_T.LICENSE_STATE = #{vo.licenseState,jdbcType=VARCHAR} or #{vo.licenseState,jdbcType=VARCHAR} is null)" +
            " and (RSBT_STATION_T.ST_D_TEC_TYPE = #{vo.netType,jdbcType=VARCHAR} or #{vo.netType,jdbcType=VARCHAR} is null)" +
            " order by RSBT_LICENSE.LICENSE_CODE) c where c.rn = 1")
    List<LicenseDTO> findAllPageByWuWei1(@Param("vo") LicenseVO vo, @Param("usersDTO") UsersDTO usersDTO);

    /**
     * 分页条件查询 wuwei
     *
     * @param vo LicenseVO
     * @param usersDTO   usersDTO
     * @return list
     */
    @Select("<script>" +
            "select * from (select RSBT_STATION_BAK.STATION_GUID as stationGuid, RSBT_LICENSE.GUID as GUID,RSBT_STATION_BAK.STAT_NAME AS stationName,RSBT_STATION_T.ST_C_CODE AS stationCode," +
            " RSBT_STATION_BAK.STAT_ADDR AS location, RSBT_STATION_T.ST_D_TEC_TYPE as netType,RSBT_STATION_BAK.STAT_LG AS longitude,RSBT_LICENSE.LICENSE_DATE_B," +
            " RSBT_STATION_BAK.STAT_LA AS latitude,RSBT_STATION.APP_CODE AS applyTableCode,RSBT_ORG.ORG_NAME AS orgName,RSBT_LICENSE.LICENSE_DATE_E,RSBT_LICENSE.LICENSE_CODE," +
            " RSBT_LICENSE_T.LICENSE_STATE AS LICENSESTATE, row_number() over (partition by RSBT_STATION_BAK.ST_C_CODE order by RSBT_STATION_BAK.BAK_DATE desc) rn" +
            " from RSBT_STATION_BAK INNER JOIN RSBT_LICENSE ON RSBT_LICENSE.STATION_GUID = RSBT_STATION_BAK.STATION_GUID" +
            " INNER JOIN RSBT_LICENSE_T ON RSBT_LICENSE.GUID = RSBT_LICENSE_T.GUID INNER JOIN RSBT_STATION ON RSBT_STATION.GUID = RSBT_STATION_BAK.STATION_GUID" +
            " INNER JOIN RSBT_STATION_T ON RSBT_LICENSE.STATION_GUID = RSBT_STATION_T.GUID LEFT JOIN RSBT_ORG_APPENDIX ON RSBT_STATION_BAK.ORG_TYPE = RSBT_ORG_APPENDIX.TYPE" +
            " LEFT JOIN RSBT_ORG ON RSBT_ORG_APPENDIX.GUID = RSBT_ORG.GUID where RSBT_ORG_APPENDIX.STATUS = 1 and RSBT_STATION_BAK.IS_SHOW = 1" +
            "<if test='vo.orgName != null and vo.orgName != \"\"'>" +
            "   AND RSBT_ORG.ORG_NAME = #{vo.orgName,jdbcType=VARCHAR} " +
            "</if>" +
            "<if test='vo.stationName != null and vo.stationName != \"\"'>" +
            "   AND RSBT_STATION_BAK.STAT_NAME LIKE CONCAT('%', #{vo.stationName}, '%') " +
            "</if>" +
           /* " and (RSBT_ORG.ORG_NAME = #{vo.orgName,jdbcType=VARCHAR} or #{vo.orgName,jdbcType=VARCHAR} is null)" +
            " and (RSBT_STATION_BAK.STAT_NAME like concat(concat('%',#{vo.stationName,jdbcType=VARCHAR}),'%') or #{vo.stationName,jdbcType=VARCHAR} is null)" +*/
            " and  RSBT_STATION.STAT_AREA_CODE in (" +
            "    WITH RECURSIVE RegionTree AS (" +
            "        SELECT ID, PARENT_ID, CODE FROM SYS_REGION WHERE ID = #{usersDTO.regionId,jdbcType=VARCHAR}" +
            "        UNION ALL" +
            "        SELECT r.ID, r.PARENT_ID, r.CODE FROM SYS_REGION r INNER JOIN RegionTree t ON r.PARENT_ID = t.ID" +
            "    )" +
            "    SELECT CODE FROM RegionTree" +
            " )" +
            "<if test='vo.startDate != null'>" +
            "   AND RSBT_LICENSE.License_Date_E &gt;= #{vo.startDate, jdbcType=DATE} " +
            "</if>" +
            "<if test='vo.endDate != null'>" +
            "   AND RSBT_LICENSE.License_Date_E &lt;= #{vo.endDate, jdbcType=DATE} " +
            "</if>" +
            "<if test='vo.licenseCode != null and vo.licenseCode != \"\"'>" +
            "   AND RSBT_LICENSE.LICENSE_CODE LIKE CONCAT('%', #{vo.licenseCode}, '%') " +
            "</if>" +
            "<if test='vo.stationCode != null and vo.stationCode != \"\"'>" +
            "   AND RSBT_STATION_T.ST_C_CODE LIKE CONCAT('%', #{vo.stationCode}, '%') " +
            "</if>" +
            "<if test='vo.licenseState != null and vo.licenseState != \"\"'>" +
            "   AND RSBT_LICENSE_T.LICENSE_STATE = #{vo.licenseState,jdbcType=VARCHAR} " +
            "</if>" +
            "<if test='vo.netType != null and vo.netType != \"\"'>" +
            "   AND RSBT_LICENSE_T.ST_D_TEC_TYPE = #{vo.netType,jdbcType=VARCHAR} " +
            "</if>" +
          /*  " and (RSBT_LICENSE.License_Date_E <= #{vo.stationStartDate,jdbcType=DATE} or #{vo.stationStartDate,jdbcType=DATE} is null)" +
            " and (RSBT_LICENSE.LICENSE_CODE like concat(concat('%',#{vo.licenseCode,jdbcType=VARCHAR}),'%') or #{vo.licenseCode,jdbcType=VARCHAR} is null)" +
            " and (RSBT_STATION_T.ST_C_CODE like concat(concat('%',#{vo.stationCode,jdbcType=VARCHAR}),'%') or #{vo.stationCode,jdbcType=VARCHAR} is null)" +
            " and (RSBT_LICENSE_T.LICENSE_STATE = #{vo.licenseState,jdbcType=VARCHAR} or #{vo.licenseState,jdbcType=VARCHAR} is null)" +
            " and (RSBT_STATION_T.ST_D_TEC_TYPE = #{vo.netType,jdbcType=VARCHAR} or #{vo.netType,jdbcType=VARCHAR} is null)" +*/
            " order by RSBT_LICENSE.LICENSE_CODE) c where c.rn = 1 " +
            "</script>")
    List<LicenseDTO> findAllPageByWuWei2(@Param("vo") LicenseVO vo, @Param("usersDTO") UsersDTO usersDTO);

    /**
     * 分页条件查询 wuwei
     *
     * @param vo LicenseVO
     * @param usersDTO   usersDTO
     * @return list
     */
    @Select("select * from (select RSBT_STATION_BAK.STATION_GUID as stationGuid, RSBT_LICENSE.GUID as GUID,RSBT_STATION_BAK.STAT_NAME AS stationName,RSBT_STATION_T.ST_C_CODE AS stationCode," +
            " RSBT_STATION_BAK.STAT_ADDR AS location, RSBT_STATION_T.ST_D_TEC_TYPE as netType,RSBT_STATION_BAK.STAT_LG AS longitude,RSBT_LICENSE.LICENSE_DATE_B," +
            " RSBT_STATION_BAK.STAT_LA AS latitude,RSBT_STATION.APP_CODE AS applyTableCode,RSBT_ORG.ORG_NAME AS orgName,RSBT_LICENSE.LICENSE_DATE_E,RSBT_LICENSE.LICENSE_CODE," +
            " RSBT_LICENSE_T.LICENSE_STATE AS LICENSESTATE, row_number() over (partition by RSBT_STATION_BAK.ST_C_CODE order by RSBT_STATION_BAK.BAK_DATE desc) rn" +
            " from RSBT_STATION_BAK INNER JOIN RSBT_LICENSE ON RSBT_LICENSE.STATION_GUID = RSBT_STATION_BAK.STATION_GUID" +
            " INNER JOIN RSBT_LICENSE_T ON RSBT_LICENSE.GUID = RSBT_LICENSE_T.GUID INNER JOIN RSBT_STATION ON RSBT_STATION.GUID = RSBT_STATION_BAK.STATION_GUID" +
            " INNER JOIN RSBT_STATION_T ON RSBT_LICENSE.STATION_GUID = RSBT_STATION_T.GUID LEFT JOIN RSBT_ORG_APPENDIX ON RSBT_STATION_BAK.ORG_TYPE = RSBT_ORG_APPENDIX.TYPE" +
            " LEFT JOIN RSBT_ORG ON RSBT_ORG_APPENDIX.GUID = RSBT_ORG.GUID where RSBT_ORG_APPENDIX.STATUS = 1 and RSBT_STATION_BAK.IS_SHOW = 1" +
            " and (RSBT_ORG.ORG_NAME = #{vo.orgName,jdbcType=VARCHAR} or #{vo.orgName,jdbcType=VARCHAR} is null)" +
            " and (RSBT_STATION_BAK.STAT_NAME like concat(concat('%',#{vo.stationName,jdbcType=VARCHAR}),'%') or #{vo.stationName,jdbcType=VARCHAR} is null)" +
            " and (RSBT_STATION.STAT_AREA_CODE like concat(#{vo.licenseCountyStr,jdbcType=VARCHAR},'%'))" +
            " and (RSBT_LICENSE.License_Date_E <= #{vo.stationStartDate,jdbcType=DATE} or #{vo.stationStartDate,jdbcType=DATE} is null)" +
            " and (RSBT_LICENSE.LICENSE_CODE like concat(concat('%',#{vo.licenseCode,jdbcType=VARCHAR}),'%') or #{vo.licenseCode,jdbcType=VARCHAR} is null)" +
            " and (RSBT_STATION_T.ST_C_CODE like concat(concat('%',#{vo.stationCode,jdbcType=VARCHAR}),'%') or #{vo.stationCode,jdbcType=VARCHAR} is null)" +
            " and (RSBT_LICENSE_T.LICENSE_STATE = #{vo.licenseState,jdbcType=VARCHAR} or #{vo.licenseState,jdbcType=VARCHAR} is null)" +
            " and (RSBT_STATION_T.ST_D_TEC_TYPE = #{vo.netType,jdbcType=VARCHAR} or #{vo.netType,jdbcType=VARCHAR} is null)" +
            " order by RSBT_LICENSE.LICENSE_CODE) c where c.rn = 1")
    List<LicenseDTO> findAllPageByWuWei3(@Param("vo") LicenseVO vo, @Param("usersDTO") UsersDTO usersDTO);

    /**
     * 根据基站查询详情
     */
    @Select("select RSBT_LICENSE.LICENSE_CODE as licenseCode,RSBT_STATION.STAT_NAME as stationName,RSBT_ORG.ORG_NAME as licensee, " +
            "RSBT_ORG.ORG_SUP_CODE as licenseeNo,RSBT_STATION.STAT_LG as longitude, " +
            "RSBT_STATION.STAT_LA as latitude,RSBT_STATION.STAT_ADDR as statAddr,RSBT_STATION_T.ST_C_CODE as stationCode, " +
            "RSBT_NET.NET_TS as stationType,RSBT_LICENSE.LICENSE_DATE_B as licenseStartDate, " +
            "RSBT_LICENSE.LICENSE_DATE_E as licenseEndDate, " +
            "(select FILE_NO from FSA_CHECK_RULE where FSA_CHECK_RULE.ORG_GUID = RSBT_net.GUID  " +
            "and FSA_CHECK_RULE.NET_TS = RSBT_NET.NET_TS and rownum = 1 ) as licenceNo,RSBT_STATION.GUID  " +
            "FROM RSBT_LICENSE,RSBT_STATION,RSBT_STATION_T,RSBT_ORG,RSBT_NET,RSBT_ORG_APPENDIX  " +
            "where RSBT_STATION.GUID = #{stationGuid} " +
            "and RSBT_STATION.GUID = RSBT_LICENSE.STATION_GUID " +
            "and RSBT_ORG_APPENDIX.GUID = RSBT_ORG.GUID " +
            "and RSBT_STATION.NET_GUID = RSBT_net.GUID   " +
            "and RSBT_NET.ORG_GUID = RSBT_ORG.GUID " +
            "and RSBT_STATION.GUID = RSBT_STATION_T.GUID")
    @Results({
            @Result(property = "iSectionDTOList", column = "STATION_GUID", many = @Many(select = "com.caict.bsm.project.domain.business.repository.license.LicenseMapper.getISectionDTOList"))}
    )
    LicensePdfDTO findOneFDFByStationGuid(@Param("stationGuid") String stationGuid);

    /**
     * 根据基站数组查询详情
     */
    @Select({"<script>",
            "select RSBT_LICENSE.LICENSE_CODE as licenseCode,RSBT_STATION.STAT_NAME as stationName,RSBT_ORG.ORG_NAME as licensee,RSBT_ORG.ORG_SUP_CODE as licenseeNo,RSBT_STATION.STAT_LG as longitude,",
            " RSBT_STATION.STAT_LA as latitude,RSBT_STATION.STAT_ADDR as statAddr,RSBT_STATION_T.ST_C_CODE as stationCode,RSBT_NET.NET_TS as stationType,RSBT_LICENSE.LICENSE_DATE_B as licenseStartDate,",
            " RSBT_LICENSE.LICENSE_DATE_E as licenseEndDate,(select FILE_NO from FSA_CHECK_RULE where FSA_CHECK_RULE.ORG_GUID = RSBT_net.GUID and FSA_CHECK_RULE.NET_TS = RSBT_NET.NET_TS and rownum = 1 ) as licenceNo,RSBT_STATION.GUID ",
            " FROM RSBT_LICENSE,RSBT_STATION,RSBT_STATION_T,RSBT_ORG,RSBT_NET ",
            " where RSBT_STATION.GUID = RSBT_LICENSE.STATION_GUID ",
            " and RSBT_NET.ORG_GUID = RSBT_ORG.GUID ",
            " and RSBT_STATION.NET_GUID = RSBT_net.GUID ",
            " and RSBT_STATION.GUID = RSBT_STATION_T.GUID ",
            "and RSBT_STATION.GUID in ",
            "<foreach collection='stationGuids' item='stationGuid' open='(' separator=',' close=')'>",
            "#{stationGuid}",
            "</foreach>",
            "</script>"})
    @Results({
            @Result(property = "stationGuid", column = "GUID"),
            @Result(property = "iSectionDTOList", column = "GUID", javaType = List.class, many = @Many(select = "getISectionDTOList"))}
    )
    List<LicensePdfDTO> findOneFDFByStationGuids(@Param("stationGuids") String[] stationGuids);

    /**
     * 根据基站数组查询详情
     */
    @Select("select RSBT_LICENSE.LICENSE_CODE as licenseCode,RSBT_STATION.STAT_NAME as stationName,RSBT_ORG.ORG_NAME as licensee,RSBT_ORG.ORG_SUP_CODE as licenseeNo,RSBT_STATION.STAT_LG as longitude, " +
            "RSBT_STATION.STAT_LA as latitude,RSBT_STATION.STAT_ADDR as statAddr,RSBT_STATION_BAK.ST_C_CODE as stationCode,RSBT_STATION_BAK.COUNTY as countyName,RSBT_NET.NET_TS as stationType,RSBT_LICENSE.LICENSE_DATE_B as licenseStartDate, " +
            "RSBT_LICENSE.LICENSE_DATE_E as licenseEndDate,(select FILE_NO from FSA_CHECK_RULE where FSA_CHECK_RULE.ORG_GUID = RSBT_net.GUID and FSA_CHECK_RULE.NET_TS = RSBT_NET.NET_TS and rownum = 1 ) as licenceNo,RSBT_STATION.GUID  " +
            "FROM RSBT_LICENSE,RSBT_STATION,RSBT_STATION_BAK,RSBT_STATION_T,RSBT_ORG,RSBT_NET  " +
            "where RSBT_STATION.GUID = RSBT_LICENSE.STATION_GUID  " +
            "and RSBT_NET.ORG_GUID = RSBT_ORG.GUID  " +
            "and RSBT_STATION_BAK.STATION_GUID = RSBT_STATION.GUID " +
            "and RSBT_STATION_BAK.IS_SHOW = 1 " +
            "and RSBT_STATION.NET_GUID = RSBT_net.GUID  " +
            "and RSBT_STATION.GUID = RSBT_STATION_T.GUID  " +
            "and RSBT_STATION.STAT_AREA_CODE =  #{licenseCountyCode} " +
            "and RSBT_ORG.ORG_NAME = #{orgName} ")
    @Results({
            @Result(property = "stationGuid", column = "GUID"),
            @Result(property = "iSectionDTOList", column = "GUID", javaType = List.class, many = @Many(select = "getISectionDTOList"))}
    )
    List<LicensePdfDTO> downloadLicensesByCounty(@Param("licenseCountyCode") String licenseCountyCode, @Param("orgName") String orgName);

    /**
     * 根据基站查询详细扇区、设备、参数
     */
    @Select("select distinct RSBT_FREQ.FREQ_EFB || ' - ' || RSBT_FREQ.FREQ_EFE as freqEf, " +
            "RSBT_FREQ.FREQ_RFB || ' - ' || RSBT_FREQ.FREQ_RFE as freqRf,(RSBT_FREQ.FREQ_EFE - RSBT_FREQ.FREQ_EFB) as freqBand,RSBT_EQU.EQU_AUTH as equAuth, " +
            "RSBT_ANTFEED.ANT_GAIN as antGain,RSBT_ANTFEED.ANT_POLE as antEpole,RSBT_ANTFEED.ANT_HIGHT as antHight  " +
            "from RSBT_ANTFEED,RSBT_EQU,RSBT_FREQ,RSBT_EAF  " +
            "where RSBT_EAF.Equ_Guid = RSBT_EQU.guid " +
            "and RSBT_ANTFEED.guid = RSBT_EAF.Ant_Guid  " +
            "and Rsbt_Freq.guid = RSBT_EAF.Freq_Guid " +
            "and RSBT_ANTFEED.STATION_GUID = RSBT_EAF.STATION_GUID and RSBT_EQU.Station_Guid = RSBT_EAF.STATION_GUID and RSBT_FREQ.STATION_GUID = RSBT_EAF.STATION_GUID and RSBT_EAF.STATION_GUID = RSBT_EAF.STATION_GUID " +
//            "and RSBT_EQU.guid = RSBT_EQU_T.guid " +
            "and RSBT_EAF.STATION_GUID = #{stationGuid}")
    List<ISectionDTO> getISectionDTOList(@Param("stationGuid") String stationGuid);

    /**
     * 查询出已发，未发，过期，停用的执照数量
     *
     * @return
     */
    @Select("SELECT SUM(CASE WHEN RSBT_STATION_BAK.IS_SHOW = '1' THEN 1 ELSE 0 END) alreadyGrant, " +
            "SUM(CASE WHEN RSBT_STATION_BAK.IS_SHOW = '3' THEN 1 ELSE 0 END) notGrant, " +
            "SUM(CASE WHEN RSBT_LICENSE_T.LICENSE_STATE= 2 THEN 1 ELSE 0 END) overDue, " +
            "SUM(CASE WHEN RSBT_LICENSE_T.LICENSE_STATE= 3 THEN 1 ELSE 0 END) stopUse " +
            "FROM RSBT_LICENSE,RSBT_LICENSE_T,RSBT_STATION_APPENDIX,RSBT_STATION_BAK WHERE RSBT_LICENSE.GUID=RSBT_LICENSE_T.GUID " +
            "AND RSBT_LICENSE.STATION_GUID = RSBT_STATION_APPENDIX.GUID AND RSBT_STATION_BAK.STATION_GUID = RSBT_LICENSE.STATION_GUID ")
    LicenseStatisticDTO getStatisticCount();


    @Select("select * from RSBT_LICENSE where STATION_GUID = #{stationGuid}")
    RsbtLicense selectByStationGuid(@Param("stationGuid") String stationGuid);

    @Update({"<script>",
            "update RSBT_LICENSE set app_code = #{appCode} ",
            "where RSBT_LICENSE.station_guid in ",
            "<foreach collection='stationGuids' item='stationGuid' open='(' separator=',' close=')'>",
            "#{stationGuid}",
            "</foreach>",
            "</script>"})
    int updateBatchByStationGuid(@Param("stationGuids") List<String> stationGuids, @Param("appCode") String appCode);

    @Update("UPDATE RSBT_LICENSE SET APP_CODE = #{newAppCode} WHERE APP_CODE = #{oldAppCode}")
    int updateAppCode(@Param("oldAppCode") String oldAppCode, @Param("newAppCode") String newAppCode);

    /**
     * 根据stationId 删除执照
     *
     * @param stationGuids stationGuids
     */
    @Delete("<script>" +
            "delete from RSBT_LICENSE where STATION_GUID in " +
            " <foreach item='item' collection='stationGuids' separator=',' open='(' close=')' index=''>" +
            "#{item} " +
            "</foreach>" +
            "</script>")
    void deleteByStationIds(@Param("stationGuids") List<String> stationGuids);

    @Select("<script>" +
            "select * from RSBT_LICENSE where STATION_GUID in " +
            " <foreach item='item' collection='stationGuids' separator=',' open='(' close=')' index=''>" +
            "#{item} " +
            "</foreach>" +
            "</script>")
    List<RsbtLicense> selectByStationIds(@Param("stationGuids") List<String> stationGuids);
}
