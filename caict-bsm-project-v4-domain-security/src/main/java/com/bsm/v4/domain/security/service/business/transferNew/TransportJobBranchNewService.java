package com.bsm.v4.domain.security.service.business.transferNew;

import com.bsm.v4.domain.security.mapper.business.transferNew.TransportJobBranchNewMapper;
import com.bsm.v4.system.model.dto.business.transfer_in.SchedualDataDTO;
import com.bsm.v4.system.model.vo.business.transferNew.TransportJobBranchNewNewVO;
import com.bsm.v4.system.model.entity.business.transferNew.TransportJobBranchNew;
import com.caictframework.data.service.BasicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 按地市拆分的子任务表Service
 */
@Service
public class TransportJobBranchNewService extends BasicService<TransportJobBranchNew> {

    @Autowired
    private TransportJobBranchNewMapper transportJobBranchNewMapper;


    /**
     * 调整统计数据（新方法，只处理正确数据统计）
     * @param id 子任务ID
     * @param totalCountDelta 总数变化量（可正可负）
     * @return 更新的记录数
     */
    public int adjustTotalCount(Long id, int totalCountDelta) {
        if (id == null) {
            return 0;
        }
        return transportJobBranchNewMapper.adjustStatistics(id, totalCountDelta, 0);
    }

    /**
     * 批量调整多个分支的统计数据（优化版本）
     * @param branchCountMap 分支ID -> 总数变化量的映射
     * @return 更新的记录数
     */
    public int batchAdjustTotalCount(Map<Long, Integer> branchCountMap) {
        if (branchCountMap == null || branchCountMap.isEmpty()) {
            return 0;
        }

        int totalUpdated = 0;
        // 逐个更新每个分支的计数（考虑到数据一致性，使用单条更新）
        for (Map.Entry<Long, Integer> entry : branchCountMap.entrySet()) {
            Long branchId = entry.getKey();
            Integer count = entry.getValue();
            if (branchId != null && count != null && count > 0) {
                totalUpdated += adjustTotalCount(branchId, count);
            }
        }

        return totalUpdated;
    }

    /**
     * 根据jobId和regionCode查找或创建TransportJobBranchNew（新方法，推荐使用）
     * @param jobId 任务ID
     * @param regionCode 地区代码（市级）
     * @param regionName 地区名称（用于显示）
     * @return TransportJobBranchNew实例
     */
    public TransportJobBranchNew findOrCreateByJobIdAndRegionCode(Long jobId, String regionCode, String regionName) {
        // 先查找是否已存在
        TransportJobBranchNew existing = transportJobBranchNewMapper.findByJobIdAndRegionCode(jobId, regionCode);
        if (existing != null) {
            return existing;
        }

        // 不存在则创建新的
        TransportJobBranchNew branch = new TransportJobBranchNew();
        branch.setJobId(jobId);
        branch.setRegionCode(regionCode);  // 设置地区代码
        branch.setRegionName(regionName);  // 设置地区名称
        branch.setStatus("created");
        branch.setTotalCount(0);  // 初始化为0
        branch.setErrorCount(0);  // 初始化为0
        branch.setCreatedAt(new Date());
        branch.setUpdatedAt(new Date());

        // 保存并确保ID正确设置
        Object savedId = save(branch);
        if (savedId != null) {
            if (savedId instanceof String) {
                branch.setId(Long.valueOf((String) savedId));
            } else if (savedId instanceof Long) {
                branch.setId((Long) savedId);
            }
            // 如果save方法已经设置了ID到实体对象中，则不需要额外处理
        }

        return branch;
    }

    /**
     * 根据任务ID删除所有分支记录
     * @param jobId 任务ID
     * @return 删除的记录数
     */
    public int deleteByJobId(Long jobId) {
        return transportJobBranchNewMapper.deleteByJobId(jobId);
    }

    /**
     * 根据任务ID更新分支状态
     */
    public void updateByJobId(Long jobId, String status) {
        transportJobBranchNewMapper.updateByJobId(jobId, status);
    }

    /**
     * 根据任务、区域查询
     */
    public TransportJobBranchNewNewVO findOneByJobUserTypeGen(String jobId, String regionCode) {
        return transportJobBranchNewMapper.findOneByJobUserTypeGen(jobId,regionCode);
    }

    /**
     * 根据任务ID查询所有分支
     * @param jobId 任务ID
     * @return 分支列表
     */
    public List<TransportJobBranchNew> findByJobId(Long jobId) {
        return transportJobBranchNewMapper.findByJobId(jobId);
    }
}
